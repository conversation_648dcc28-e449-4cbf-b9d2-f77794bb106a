<?php
// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

include 'config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Check if invoice ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: invoices.php');
    exit;
}

$invoice_id = $_GET['id'];

// Get invoice with user access check
$stmt = $pdo->prepare("SELECT * FROM invoices 
                      JOIN clients ON invoices.client_id = clients.id
                      WHERE invoices.id = ? AND invoices.user_id = ?");
$stmt->execute([$invoice_id, $_SESSION['user_id']]);
$invoice = $stmt->fetch();

if (!$invoice) {
    header('Location: invoices.php');
    exit;
}

$items = $pdo->query("SELECT * FROM invoice_items 
                     JOIN items ON invoice_items.item_id = items.id
                     WHERE invoice_id = $invoice_id")->fetchAll();

// Calculate Totals
$subtotal = 0;
foreach($items as $item){
    $subtotal += $item['unit_price'] * $item['quantity'];
}

$discount_rate = $subtotal * ($invoice['discount_rate'] / 100);
$discount_fixed = $invoice['discount_fixed'];
$total = $subtotal - ($discount_rate + $discount_fixed);
$tax = $total * ($invoice['vat_rate'] / 100);
$total_grand = $total + $tax;

$invoice_for = $invoice['invoice_for'];
$service_type = $invoice['service_type'];
$special_notes = $item['special_notes'];

// Add null checks for dates
$invoice_date = !empty($invoice['invoice_date']) ? date("d M Y", strtotime($invoice['invoice_date'])) : 'Not Set';
$quote_date = !empty($invoice['quote_date']) ? date("d M Y", strtotime($invoice['quote_date'])) : 'Not Set';
$invoice_id_date = !empty($invoice['quote_date']) ? date("ymd", strtotime($invoice['quote_date'])) : date("ymd");
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo "Quotation: KL-QTN-" . $_GET['id']; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Montserrat', sans-serif;
            font-size: 12px;
            background-color: #f0f0f0;
            padding: 20px;
        }
        .print-wrap {
            width: 210mm;
            min-height: 297mm;
            padding: 15mm;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            position: relative;
        }
        .action-buttons {
            position: fixed;
            top: 20px;
            left: 0;
            right: 0;
            z-index: 1000;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
        }
        .action-buttons .btn {
            padding: 8px 20px;
            font-weight: 500;
        }

        footer {
            font-size: 10px;
            position: absolute !important;
            bottom: 10mm !important;
            left: 0 !important;
            right: 0 !important;
            width: 100% !important;
            padding: 0 15mm !important;
        }

        @media print {
            body {
                background: none;
                padding: 0;
            }
            .print-wrap {
                width: 100%;
                min-height: auto;
                padding: 5mm;
                margin: 0;
                box-shadow: none;
            }
            .action-buttons {
                display: none;
            }
            footer {
                bottom: 0 !important;
                padding: 0 5mm !important;
            }
        }
        .invoice-header {
            border-bottom: 2px solid #dee2e6;
            margin-bottom: 2rem;
        }
        .total-row {
            font-weight: bold;
        }
        .grand-total-row td,
        .table-light th {
            background-color: #f3f3f3;
        }
        .grand-total-row *,
        .table-light {
            font-size: 14px;
        }
        .grand-total-row em {
            font-weight: normal;
            font-size: 10px;
            display: block;
        }
        .total-row {
            font-size: 13px;
        }
        .signature-line {
            border-top: 1px solid #000;
            width: 200px;
            margin-top: 50px;
        }
        .tax-note {
            background-color:#eee;
            padding: 12px;
            border-radius: 4px;
            margin: 20px 0;
            font-size: 13px;
        }
        table.table * {
            border-color: #000;
        }
        h3 {
            font-size: 21px;
        }
        h4 {
            font-size: 17px;
        }
        h5 {
            font-size: 15px;
        }
        h6 {
            font-size: 12px;
        }
        .kl-payment,
        .kl-summary {
            font-size: 11px;
        }
        .kl-terms ol {
            padding: 0 0 0 13px;
            margin: 0;
            font-size: 11px;
        }
        .kl-item strong {
            font-size: 13px;
        }
        .kl-item p {
            font-size: 11px;
        }
        .kl-sm-font {
            font-size: 11px;
        }
        .kl-sm-font em {
            font-size: 10px;
        }
    </style>
</head>
<body>
    <div class="action-buttons">
        <a href="javascript:history.back()" class="btn btn-secondary">
            <i class="bi bi-arrow-left"></i> Back
        </a>
        <button onclick="window.print()" class="btn btn-primary">
            <i class="bi bi-printer"></i> Print
        </button>
    </div>

    <div class="container-fluid print-wrap mt-2 mb-5">
        <header class="mb-5 text-end">
            <img src="assets/img/logo.svg" class="img-fluid" width="300">
        </header>
        <div class="card-wrap pt-3">
            <div class="card-body">
                <!-- Client Info -->
                <div class="row mb-4 pb-2">
                    <div class="col-8">
                        <h5 class="fw-bold mb-0">Client: <?= $invoice['name'] ?></h5>
                        <p class="mb-0"><strong>Address</strong>: <?= $invoice['address'] ?></p>
                    </div>
                    <div class="col-4 text-end">
                        <p class="mb-0"><strong>Quotation ID</strong>: KL-QTN-<?= $invoice_id_date ?>-<?= $invoice_id ?></p>
                        <p class="mb-0"><strong>Date</strong>: <?= $quote_date ?></p>
                    </div>
                </div>

                <!-- Title -->
                <div class="row mb-0">
                    <div class="col-12 text-center">
                        <h3 class="mb-0 fw-bold">Quotation</h3>
                        <h4 class="mb-0 fw-bold"><?= $invoice_for ?></h4>
                        <p>(<?= $service_type ?>)</p>
                    </div>
                </div>

                <!-- Bill Table -->
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead class="table-light">
                            <tr>
                                <th class="text-center" width="5%">SL.</th>
                                <th width="30%">Services/Items</th>
                                <th width="30%">Description</th>
                                <th class="text-end" width="20%">Rate (BDT)</th>
                                <th width="15%" class="text-end">Price (BDT)</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $i = 1; foreach($items as $item): ?>
                                <tr>
                                    <td class="text-center"><?= $i++ ?>.</td>
                                    <td class="kl-item">
                                        <strong><?= $item['name'] ?></strong>
                                        <p class="mb-0"><?= $item['special_notes'] ?></p>
                                    </td>
                                    <td class="kl-item kl-sm-font">
                                        <?= $item['description'] ?>
                                    </td>
                                    <td class="kl-item text-end kl-sm-font">
                                        <?= $item['summary'] ?> x <?= $item['quantity'] ?><br>
                                        <em class="kl-summary">(Rate @ BDT <?= number_format($item['unit_price'], 2) ?>)</em>
                                    </td>
                                    <td class="text-end"><?= number_format($item['unit_price'] * $item['quantity'], 2) ?></td>
                                </tr>
                            <?php endforeach; ?>

                            <tr class="text-end total-row">
                                <th colspan="4">SUBTOTAL</th>
                                <td><?= number_format($subtotal, 2) ?></td>
                            </tr>

                            <?php if ($invoice['discount_rate'] > 0 || $invoice['discount_fixed'] > 0): ?>
                                <tr class="text-end total-row">
                                    <td colspan="4">DISCOUNT
                                        <?php if ($invoice['discount_rate'] > 0): ?>
                                            (<?= $invoice['discount_rate'] ?>%)
                                        <?php else: ?>
                                            (Onetime)
                                        <?php endif; ?>
                                        <div style="font-weight: normal; font-size: 13px; font-style: italic;"><?= $invoice['discount_notes'] ?></div>
                                    </td>
                                    <td>
                                        <?php if ($invoice['discount_rate'] > 0): ?>
                                            -<?= number_format($discount_rate, 2) ?>
                                        <?php else: ?>
                                            -<?= number_format($invoice['discount_fixed'], 2) ?>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php if ($invoice['vat_rate'] > 0): ?>
                                <tr class="text-end total-row">
                                    <td colspan="4">TOTAL</td>
                                    <td><?= number_format($total, 2) ?></td>
                                </tr>
                                <?php endif; ?>
                            <?php endif; ?>

                            <?php if ($invoice['vat_rate'] > 0): ?>
                            <tr class="text-end total-row">
                                <td colspan="4">VAT (<?= $invoice['vat_rate'] ?>%)</td>
                                <td><?= number_format($tax, 2) ?></td>
                            </tr>
                            <?php endif; ?>
                            <tr class="text-end total-row grand-total-row">
                                <td colspan="4">GRAND TOTAL</td>
                                <td>
                                    <?= number_format($total_grand, 2) ?>
                                    <?php if ($invoice['vat_rate'] == 0): ?>
                                        <em>(Excluding VAT)</em>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Payment Details -->
                <!-- <div class="row mt-4 kl-payment">
                    <div class="col-12">
                        <p class="mb-4">All payments made via Electronic Funds Transfer (EFT) or A/C Payee Cheque should be in favor of <strong>"Klay Technologies"</strong>.</p>
                    </div>
                </div> -->

                <!-- Terms -->
                <div class="row mt-4 kl-terms">
                    <div class="col-12">
                        <h6 class="fw-bold mb-1">N.B.</h6>
                        <ol class="mb-0">
                            <li><strong>Pricing Policy</strong>: Prices may change due to currency rates or service charges.</li>
                            <li><strong>Feature Additions</strong>: Additional features may increase cost and timeline.</li>
                            <li><strong>Project Timelines</strong>: Delays in providing documents or feedback may extend the timeline.</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>

        <footer class="text-center pt-3 mb-1">
            <p class="mb-0"><strong>Office</strong>: Flat# 1B, House# 460/A, Road# 6, Avenue# 7, Mirpur DOHS, Dhaka</p>
            <p class="mb-0"><strong>Phone</strong>: 01552454543  <strong>Email</strong>: <EMAIL>  <strong>Web</strong>: klay.tech</p>
        </footer>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            // Move footer inside print-wrap for screen display
            $('.print-wrap').append($('footer'));
            
            // Handle print event
            window.onbeforeprint = function() {
                // Move footer back to body for printing
                $('body').append($('footer'));
            };
            
            window.onafterprint = function() {
                // Move footer back inside print-wrap after printing
                $('.print-wrap').append($('footer'));
            };
        });
    </script>
</body>
</html>