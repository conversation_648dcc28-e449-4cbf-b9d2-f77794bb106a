<?php
require_once 'auth.php';
$page_title = "Klay Invoice - Invoices";
include 'includes/header.php';
include 'config.php';

// Get the current page number
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;

// Set the number of records per page
$limit = 20;

// Calculate the number of records to skip
$offset = ($page - 1) * $limit;

// Build the query with search conditions
$query = "SELECT i.id, i.invoice_for, i.service_type, i.quote_date, i.invoice_date, i.paid_date, i.vat_rate, i.payment_status,
          i.tds_certificate, i.tds_challan, i.vds_certificate, i.vds_challan, i.wo_document,
          i.discount_rate, i.discount_fixed, c.name AS client_name,
          (SELECT SUM(unit_price * quantity) FROM invoice_items WHERE invoice_id = i.id) AS total_price
          FROM invoices i
          JOIN clients c ON i.client_id = c.id
          WHERE i.user_id = ?";

$params = [$_SESSION['user_id']];

// Add client name filter if provided
if (isset($_GET['client_name']) && !empty($_GET['client_name'])) {
    $query .= " AND c.name LIKE ?";
    $params[] = '%' . $_GET['client_name'] . '%';
}

// Add status filter if provided
if (isset($_GET['status']) && !empty($_GET['status'])) {
    if ($_GET['status'] == 'invoiced') {
        // For "Invoiced" filter, show entries where invoice_date is not null
        $query .= " AND i.invoice_date IS NOT NULL";
    } else {
        // For other status filters (paid, unpaid, processing)
        $query .= " AND i.payment_status = ?";
        $params[] = $_GET['status'];
    }
}

// Add date range filters if provided
if (isset($_GET['date_from']) && !empty($_GET['date_from'])) {
    // Use different date fields based on status filter
    if (isset($_GET['status']) && $_GET['status'] == 'paid') {
        // For paid invoices, use paid_date
        $query .= " AND i.paid_date >= ?";
        $params[] = $_GET['date_from'];
    } else if (isset($_GET['status']) && $_GET['status'] == 'unpaid') {
        // For unpaid invoices, use invoice_date if available, otherwise quote_date
        $query .= " AND (i.invoice_date >= ? OR (i.invoice_date IS NULL AND i.quote_date >= ?))";
        $params[] = $_GET['date_from'];
        $params[] = $_GET['date_from'];
    } else if (isset($_GET['status']) && $_GET['status'] == 'processing') {
        // For processing invoices, ONLY use invoice_date (must be available)
        $query .= " AND i.invoice_date >= ?";
        $params[] = $_GET['date_from'];
    } else if (isset($_GET['status']) && $_GET['status'] == 'invoiced') {
        // For invoiced filter, use invoice_date
        $query .= " AND i.invoice_date >= ?";
        $params[] = $_GET['date_from'];
    } else {
        // Default to quote_date for all other cases
        $query .= " AND i.quote_date >= ?";
        $params[] = $_GET['date_from'];
    }
}

if (isset($_GET['date_to']) && !empty($_GET['date_to'])) {
    // Use different date fields based on status filter
    if (isset($_GET['status']) && $_GET['status'] == 'paid') {
        // For paid invoices, use paid_date
        $query .= " AND i.paid_date <= ?";
        $params[] = $_GET['date_to'];
    } else if (isset($_GET['status']) && $_GET['status'] == 'unpaid') {
        // For unpaid invoices, use invoice_date if available, otherwise quote_date
        $query .= " AND (i.invoice_date <= ? OR (i.invoice_date IS NULL AND i.quote_date <= ?))";
        $params[] = $_GET['date_to'];
        $params[] = $_GET['date_to'];
    } else if (isset($_GET['status']) && $_GET['status'] == 'processing') {
        // For processing invoices, ONLY use invoice_date (must be available)
        $query .= " AND i.invoice_date <= ?";
        $params[] = $_GET['date_to'];
    } else if (isset($_GET['status']) && $_GET['status'] == 'invoiced') {
        // For invoiced filter, use invoice_date
        $query .= " AND i.invoice_date <= ?";
        $params[] = $_GET['date_to'];
    } else {
        // Default to quote_date for all other cases
        $query .= " AND i.quote_date <= ?";
        $params[] = $_GET['date_to'];
    }
}

// Add ordering
$query .= " ORDER BY i.id DESC LIMIT " . (int)$offset . ", " . (int)$limit;

// Prepare the query
$stmt = $pdo->prepare($query);

// Execute the query with parameters
try {
    $stmt->execute($params);
    // Get the results from the query
    $results = $stmt->fetchAll();
} catch (PDOException $e) {
    echo '<div class="alert alert-danger">Error executing query: ' . $e->getMessage() . '</div>';
    echo '<div class="alert alert-info">Query: ' . $query . '</div>';
    echo '<div class="alert alert-info">Parameters: ' . print_r($params, true) . '</div>';
    $results = [];
}

// Count query for pagination
$count_query = "SELECT COUNT(*) FROM invoices i JOIN clients c ON i.client_id = c.id WHERE i.user_id = ?";
$count_params = [$_SESSION['user_id']];

// Add the same conditions to the count query
if (isset($_GET['client_name']) && !empty($_GET['client_name'])) {
    $count_query .= " AND c.name LIKE ?";
    $count_params[] = '%' . $_GET['client_name'] . '%';
}

if (isset($_GET['status']) && !empty($_GET['status'])) {
    if ($_GET['status'] == 'invoiced') {
        // For "Invoiced" filter, show entries where invoice_date is not null
        $count_query .= " AND i.invoice_date IS NOT NULL";
    } else {
        // For other status filters (paid, unpaid, processing)
        $count_query .= " AND i.payment_status = ?";
        $count_params[] = $_GET['status'];
    }
}

if (isset($_GET['date_from']) && !empty($_GET['date_from'])) {
    if (isset($_GET['status']) && $_GET['status'] == 'paid') {
        $count_query .= " AND i.paid_date >= ?";
        $count_params[] = $_GET['date_from'];
    } else if (isset($_GET['status']) && $_GET['status'] == 'unpaid') {
        $count_query .= " AND (i.invoice_date >= ? OR (i.invoice_date IS NULL AND i.quote_date >= ?))";
        $count_params[] = $_GET['date_from'];
        $count_params[] = $_GET['date_from'];
    } else if (isset($_GET['status']) && $_GET['status'] == 'processing') {
        // For processing invoices, ONLY use invoice_date (must be available)
        $count_query .= " AND i.invoice_date >= ?";
        $count_params[] = $_GET['date_from'];
    } else if (isset($_GET['status']) && $_GET['status'] == 'invoiced') {
        // For invoiced filter, use invoice_date
        $count_query .= " AND i.invoice_date >= ?";
        $count_params[] = $_GET['date_from'];
    } else {
        $count_query .= " AND i.quote_date >= ?";
        $count_params[] = $_GET['date_from'];
    }
}

if (isset($_GET['date_to']) && !empty($_GET['date_to'])) {
    if (isset($_GET['status']) && $_GET['status'] == 'paid') {
        $count_query .= " AND i.paid_date <= ?";
        $count_params[] = $_GET['date_to'];
    } else if (isset($_GET['status']) && $_GET['status'] == 'unpaid') {
        $count_query .= " AND (i.invoice_date <= ? OR (i.invoice_date IS NULL AND i.quote_date <= ?))";
        $count_params[] = $_GET['date_to'];
        $count_params[] = $_GET['date_to'];
    } else if (isset($_GET['status']) && $_GET['status'] == 'processing') {
        // For processing invoices, ONLY use invoice_date (must be available)
        $count_query .= " AND i.invoice_date <= ?";
        $count_params[] = $_GET['date_to'];
    } else if (isset($_GET['status']) && $_GET['status'] == 'invoiced') {
        // For invoiced filter, use invoice_date
        $count_query .= " AND i.invoice_date <= ?";
        $count_params[] = $_GET['date_to'];
    } else {
        $count_query .= " AND i.quote_date <= ?";
        $count_params[] = $_GET['date_to'];
    }
}

$count_stmt = $pdo->prepare($count_query);
$count_stmt->execute($count_params);
$total_records = $count_stmt->fetchColumn();

// Calculate total pages
$total_pages = ceil($total_records / $limit);
?>

<div class="container-fluid px-4">
    <div class="row mb-4">
        <div class="col">
            <h2 class="mt-4 mb-4"><i class="bi bi-file-earmark-text"></i> Invoices</h2>
        </div>
        <div class="col text-end">
            <a href="create_invoice.php" class="btn btn-primary mt-4">
                <i class="bi bi-plus-circle"></i> Create New Invoice
            </a>
        </div>
    </div>

    <!-- Search and Filter Card -->
    <div class="card mb-4 shadow-sm">
        <div class="card-header bg-light">
            <h5 class="mb-0"><i class="bi bi-search"></i> Search & Filter</h5>
        </div>
        <div class="card-body">
            <form method="get" class="mb-0">
                <div class="row g-3 align-items-center">
                    <div class="col-md-3">
                        <label class="form-label small mb-1">Client</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-person"></i></span>
                            <input type="text" name="client_name" class="form-control" placeholder="Client name..." 
                                value="<?= isset($_GET['client_name']) ? htmlspecialchars($_GET['client_name']) : '' ?>">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label small mb-1">From Date</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-calendar-date"></i></span>
                            <input type="date" name="date_from" class="form-control" 
                                placeholder="<?= date('Y-m-01') ?>"
                                value="<?= isset($_GET['date_from']) ? htmlspecialchars($_GET['date_from']) : '' ?>">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label small mb-1">To Date</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-calendar-date"></i></span>
                            <input type="date" name="date_to" class="form-control" 
                                placeholder="<?= date('Y-m-d') ?>"
                                value="<?= isset($_GET['date_to']) ? htmlspecialchars($_GET['date_to']) : '' ?>">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label small mb-1">Status</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-tag"></i></span>
                            <select name="status" class="form-select form-control">
                                <option value="">Show All</option>
                                <option value="paid" <?= (isset($_GET['status']) && $_GET['status'] == 'paid') ? 'selected' : '' ?>>Paid</option>
                                <option value="unpaid" <?= (isset($_GET['status']) && $_GET['status'] == 'unpaid') ? 'selected' : '' ?>>Unpaid</option>
                                <option value="processing" <?= (isset($_GET['status']) && $_GET['status'] == 'processing') ? 'selected' : '' ?>>Processing</option>
                                <option value="invoiced" <?= (isset($_GET['status']) && $_GET['status'] == 'invoiced') ? 'selected' : '' ?>>Invoiced</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label small mb-1">&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-search"></i> Search
                            </button>
                            <?php if(isset($_GET['client_name']) || isset($_GET['status']) || isset($_GET['date_from']) || isset($_GET['date_to'])): ?>
                                <a href="invoices.php" class="btn btn-outline-secondary ms-2">
                                    <i class="bi bi-x-circle"></i> Clear Filters
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

<?php function getStatusBadge($payment_status)
{
    $statusClass = '';
    $statusIcon = '';
    switch ($payment_status) {
        case 'paid':
            $statusClass = 'success';
            $statusIcon = 'check-circle';
            break;
        case 'unpaid':
            $statusClass = 'danger';
            $statusIcon = 'exclamation-circle';
            break;
        case 'processing':
            $statusClass = 'warning';
            $statusIcon = 'hourglass-split';
            break;
        default:
            $statusClass = 'secondary';
            $statusIcon = 'question-circle';
    }

    return "<span class='badge bg-{$statusClass} bg-opacity-10 text-{$statusClass} border border-{$statusClass} border-opacity-25'>
                <i class='bi bi-{$statusIcon} me-1'></i>" . ucfirst($payment_status) . "</span>";
}

// Display the invoices
if ($results) {
    echo '<div class="card mb-4 shadow-sm">';
    echo '<div class="card-header bg-light">';
    echo '<div class="row align-items-center">';
    echo '<div class="col"><h5 class="mb-0"><i class="bi bi-file-earmark-text"></i> Invoice List</h5></div>';
    echo '<div class="col-auto">';
    
    echo '<span class="badge bg-primary">' . $total_records . ' invoices found</span>';
    echo '</div>';
    echo '</div>';
    echo '</div>';
    
    echo '<div class="card-body p-0">';
    echo '<div class="table-responsive">';
    echo "<table class='table table-hover table-no-border table-striped table-top-align mb-0'>";
    echo "<thead class='table-dark'>";
    echo "<tr>";
    echo "<th class='ps-3 align-top'>ID</th>";
    echo "<th class='align-top'>Client Name</th>";
    echo "<th class='align-top'>Invoice Title</th>";
    echo "<th class='text-end align-top'>Price (Excl. VAT)</th>";
    echo "<th class='text-end align-top'>VAT</th>";
    echo "<th class='text-center align-top'>WO</th>";
    echo "<th class='text-center align-top'>TDS</th>";
    echo "<th class='text-center align-top'>VDS</th>";
    echo "<th class='text-center align-top'>Status</th>";
    echo "<th class='text-center align-top'>Actions</th>";
    echo "</tr>";
    echo "</thead>";
    echo "<tbody>";

    foreach ($results as $result) {
        $quote_date = !empty($result['quote_date']) ? date("d M Y", strtotime($result['quote_date'])) : 'Not Set';
        $invoice_date = !empty($result['invoice_date']) ? date("d M Y", strtotime($result['invoice_date'])) : 'Not Set';
        $paid_date = !empty($result['paid_date']) ? date("d M Y", strtotime($result['paid_date'])) : 'Not Set';
        
        // Calculate the subtotal (total before discount)
        $subtotal = $result['total_price'];
        
        // Calculate discount amount
        $discount_rate_amount = $subtotal * ($result['discount_rate'] / 100);
        $discount_fixed_amount = $result['discount_fixed'];
        $total_discount = $discount_rate_amount + $discount_fixed_amount;
        
        // Calculate the total after discount
        $total_after_discount = $subtotal - $total_discount;
        
        // Calculate VAT amount based on the discounted total
        $vat_amount = $total_after_discount * ($result['vat_rate'] / 100);
        
        // Final total price including VAT
        $final_total = $total_after_discount + $vat_amount;

        echo "<tr>";
        echo "<td class='ps-3'>" . $result['id'] . "</td>";
        echo "<td>" . $result['client_name'] . "<br><em class='text-muted small'>Quoted: " . $quote_date . "</em>";
        
        // Add invoice date if available
        if (!empty($result['invoice_date'])) {
            echo "<br><em class='text-primary small'>Invoice: " . $invoice_date . "</em>";
        }
        
        // Add paid date if available and status is paid
        if ($result['payment_status'] == 'paid' && !empty($result['paid_date'])) {
            echo "<br><em class='text-success small'>Paid: " . $paid_date . "</em>";
        }
        
        echo "</td>";
        echo "<td>" . $result['invoice_for'] . "<br><em class='text-muted small'>" . $result['service_type'] . "</em></td>";
        echo "<td class='text-end'>" . number_format($total_after_discount, 2) . "</td>";
        if ($result['vat_rate'] > 0) {
            echo "<td class='text-end'>";
            echo number_format($vat_amount, 2) . " <br><small class='text-muted'>(" . number_format($result['vat_rate'], 2) . "%)</small>";
            echo "</td>";
        } else {
            echo "<td class='text-end vat-empty'>";
            echo "<span class='text-light-emphasis text-light-empty'>-</span>";
            echo "</td>";
        }
        
        echo "<td class='text-center'>";
        if (!empty($result['wo_document'])) {
            echo "<a class='btn btn-sm btn-outline-success border' href='" . $result['wo_document'] . "' title='Download Work Order' data-bs-toggle='tooltip'>";
            echo "<i class='bi bi-download'></i></a>";
        } else {
            echo "<span class='text-light-emphasis text-light-empty'>-</span>";
        }
        echo "</td>";

        echo "<td class='text-center'>";
        if (!empty($result['tds_certificate']) || !empty($result['tds_challan'])) {
            echo "<div class='d-inline-flex gap-1'>";
            if (!empty($result['tds_certificate'])) {
                echo "<a class='btn btn-sm btn-outline-info border' href='" . $result['tds_certificate'] . "' title='TDS Certificate' data-bs-toggle='tooltip'>";
                echo "<i class='bi bi-file-earmark-text'></i></a>";
            }
            if (!empty($result['tds_challan'])) {
                echo "<a class='btn btn-sm btn-outline-info border' href='" . $result['tds_challan'] . "' title='TDS Challan' data-bs-toggle='tooltip'>";
                echo "<i class='bi bi-receipt'></i></a>";
            }
            echo "</div>";
        } else {
            echo "<span class='text-light-emphasis text-light-empty'>-</span>";
        }
        echo "</td>";

        echo "<td class='text-center'>";
        if (!empty($result['vds_certificate']) || !empty($result['vds_challan'])) {
            echo "<div class='d-inline-flex gap-1'>";
            if (!empty($result['vds_certificate'])) {
                echo "<a class='btn btn-sm btn-outline-secondary border' href='" . $result['vds_certificate'] . "' title='VDS Certificate' data-bs-toggle='tooltip'>";
                echo "<i class='bi bi-file-earmark-text'></i></a>";
            }
            if (!empty($result['vds_challan'])) {
                echo "<a class='btn btn-sm btn-outline-secondary border' href='" . $result['vds_challan'] . "' title='VDS Challan' data-bs-toggle='tooltip'>";
                echo "<i class='bi bi-receipt'></i></a>";
            }
            echo "</div>";
        } else {
            echo "<span class='text-light-emphasis text-light-empty'>-</span>";
        }
        echo "</td>";
        
        echo "<td class='text-center'>" . getStatusBadge($result['payment_status']) . "</td>";
        
        echo "<td class='text-center'>";
        echo "<div class='d-inline-flex gap-1'>";
        echo "<a href='view_invoice.php?id=" . $result['id'] . "' class='btn btn-sm btn-outline-primary border' title='View Invoice' data-bs-toggle='tooltip'>";
        echo "<i class='bi bi-eye'></i></a>";
        echo "<a href='edit_invoice_new.php?id=" . $result['id'] . "' class='btn btn-sm btn-outline-warning border' title='Edit Invoice' data-bs-toggle='tooltip'>";
        echo "<i class='bi bi-pencil'></i></a>";
        echo "<button type='button' class='btn btn-sm btn-outline-danger border' data-bs-toggle='modal' data-bs-target='#deleteModal" . $result['id'] . "' title='Delete Invoice' data-bs-toggle='tooltip'>";
        echo "<i class='bi bi-trash'></i></button>";
        echo "</div>";
        echo "</td>";
        echo "</tr>";
    }

    echo "</tbody>";
    echo "</table>";
    echo "</div>"; // table-responsive
    echo "</div>"; // card-body
    
    // Calculate the total number of pages with the same filters
    $num_pages = ceil($total_records / $limit);

    // Build the pagination URL with search parameters
    $pagination_url = 'invoices.php?';
    if (isset($_GET['client_name']) && !empty($_GET['client_name'])) {
        $pagination_url .= 'client_name=' . urlencode($_GET['client_name']) . '&';
    }
    if (isset($_GET['status']) && !empty($_GET['status'])) {
        $pagination_url .= 'status=' . urlencode($_GET['status']) . '&';
    }
    if (isset($_GET['date_from']) && !empty($_GET['date_from'])) {
        $pagination_url .= 'date_from=' . urlencode($_GET['date_from']) . '&';
    }
    if (isset($_GET['date_to']) && !empty($_GET['date_to'])) {
        $pagination_url .= 'date_to=' . urlencode($_GET['date_to']) . '&';
    }

    // Display the pagination links if there are multiple pages
    if ($num_pages > 1) {
        echo "<div class='card-footer bg-light'>";
        echo "<nav aria-label='Page navigation'>";
        echo "<ul class='pagination justify-content-center mb-0'>";
        
        // Previous page link
        if ($page > 1) {
            echo "<li class='page-item'><a class='page-link' href='" . $pagination_url . "page=" . ($page - 1) . "'><i class='bi bi-chevron-left'></i></a></li>";
        } else {
            echo "<li class='page-item disabled'><a class='page-link' href='#'><i class='bi bi-chevron-left'></i></a></li>";
        }
        
        // Page numbers
        $start_page = max(1, $page - 2);
        $end_page = min($num_pages, $page + 2);
        
        if ($start_page > 1) {
            echo "<li class='page-item'><a class='page-link' href='" . $pagination_url . "page=1'>1</a></li>";
            if ($start_page > 2) {
                echo "<li class='page-item disabled'><a class='page-link' href='#'>...</a></li>";
            }
        }
        
        for ($i = $start_page; $i <= $end_page; $i++) {
            echo "<li class='page-item" . ($i === (int)$page ? " active" : "") . "'><a class='page-link' href='" . $pagination_url . "page=" . $i . "'>" . $i . "</a></li>";
        }
        
        if ($end_page < $num_pages) {
            if ($end_page < $num_pages - 1) {
                echo "<li class='page-item disabled'><a class='page-link' href='#'>...</a></li>";
            }
            echo "<li class='page-item'><a class='page-link' href='" . $pagination_url . "page=" . $num_pages . "'>" . $num_pages . "</a></li>";
        }
        
        // Next page link
        if ($page < $num_pages) {
            echo "<li class='page-item'><a class='page-link' href='" . $pagination_url . "page=" . ($page + 1) . "'><i class='bi bi-chevron-right'></i></a></li>";
        } else {
            echo "<li class='page-item disabled'><a class='page-link' href='#'><i class='bi bi-chevron-right'></i></a></li>";
        }
        
        echo "</ul>";
        echo "</nav>";
        echo "</div>"; // card-footer
    }
    
    echo "</div>"; // card

    // Add delete modals outside the table structure
    foreach ($results as $result) {
        echo "<div class='modal fade' id='deleteModal" . $result['id'] . "' tabindex='-1' aria-labelledby='deleteModalLabel" . $result['id'] . "' aria-hidden='true'>";
        echo "<div class='modal-dialog'>";
        echo "<div class='modal-content'>";
        echo "<div class='modal-header'>";
        echo "<h5 class='modal-title' id='deleteModalLabel" . $result['id'] . "'>Confirm Delete</h5>";
        echo "<button type='button' class='btn-close' data-bs-dismiss='modal' aria-label='Close'></button>";
        echo "</div>";
        echo "<div class='modal-body text-start'>";
        echo "<p>Are you sure you want to delete invoice #" . $result['id'] . " for " . htmlspecialchars($result['client_name']) . "?</p>";
        echo "<p class='text-danger'><strong>Warning:</strong> This action cannot be undone.</p>";
        echo "</div>";
        echo "<div class='modal-footer'>";
        echo "<button type='button' class='btn btn-secondary' data-bs-dismiss='modal'>Cancel</button>";
        echo "<form action='delete_invoice.php' method='post' class='d-inline'>";
        echo "<input type='hidden' name='id' value='" . $result['id'] . "'>";
        echo "<button type='submit' class='btn btn-danger'>Delete Invoice</button>";
        echo "</form>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
    }
} else {
    echo '<div class="alert alert-info" role="alert">';
    echo '<i class="bi bi-info-circle me-2"></i> No invoices found matching your criteria.';
    echo '</div>';
}
?>
</div>

<?php include 'includes/footer.php'; ?>

<script>
// Set default date values if not already set
document.addEventListener('DOMContentLoaded', function() {
    const dateFromInput = document.querySelector('input[name="date_from"]');
    const dateToInput = document.querySelector('input[name="date_to"]');

    // If date_from is not set in the URL and the input is empty
    if (!dateFromInput.value && !window.location.search.includes('date_from=')) {
        // Set to first day of current month
        const firstDay = new Date();
        firstDay.setDate(1);
        dateFromInput.value = firstDay.toISOString().slice(0, 10);
    }

    // If date_to is not set in the URL and the input is empty
    if (!dateToInput.value && !window.location.search.includes('date_to=')) {
        // Set to today
        dateToInput.value = new Date().toISOString().slice(0, 10);
    }

    // Initialize tooltips for action buttons
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
