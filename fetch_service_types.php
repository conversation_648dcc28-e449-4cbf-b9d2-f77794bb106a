<?php
// Include the database connection file
require_once 'config.php';

// Query the database for service types
//$stmt = $pdo->prepare("SELECT DISTINCT service_type FROM invoices ORDER BY service_type ASC");
$stmt = $pdo->prepare("SELECT DISTINCT service_type FROM invoices WHERE service_type IS NOT NULL AND service_type <> '' ORDER BY service_type ASC");
if (!$stmt->execute()) {
    echo json_encode(array('error' => 'Failed to fetch service types'));
    exit;
}

// Fetch the results
$serviceTypes = $stmt->fetchAll(PDO::FETCH_ASSOC);

// If no results are found, output an empty array
if (empty($serviceTypes)) {
    echo json_encode(array());
    exit;
}

// Convert the results to JSON and output
echo json_encode($serviceTypes);