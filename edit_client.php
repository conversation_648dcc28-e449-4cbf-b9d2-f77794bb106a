<?php
require_once 'auth.php';
include 'config.php';

// Get client ID from URL
$client_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Verify client exists and belongs to user
$stmt = $pdo->prepare("SELECT * FROM clients WHERE id = ? AND user_id = ?");
$stmt->execute([$client_id, $_SESSION['user_id']]);
$client = $stmt->fetch();

if(!$client) {
    $_SESSION['error'] = "Client not found!";
    header("Location: clients.php");
    exit();
}

// Process form submission
if(isset($_POST['edit_client'])) {
    $name = trim($_POST['name'] ?? '');
    $bin = trim($_POST['bin'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $address = trim($_POST['address'] ?? '');

    // Validate required fields
    if(empty($name)) {
        $error = "Client name is required!";
    } elseif(empty($address)) {
        $error = "Address is required!";
    } else {
        // Validate email if provided
        if(!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $error = "Invalid email format!";
        } else {
            try {
                $stmt = $pdo->prepare("UPDATE clients SET name = ?, bin = ?, email = ?, address = ? WHERE id = ? AND user_id = ?");
                $stmt->execute([$name, $bin, $email, $address, $client_id, $_SESSION['user_id']]);
                $_SESSION['success'] = "Client updated successfully!";
                header("Location: clients.php");
                exit();
            } catch(PDOException $e) {
                $error = "Error updating client. Please try again.";
            }
        }
    }
}

// Set page title and include header AFTER form processing
$page_title = "Klay Invoice - Edit Client";
include 'includes/header.php';
?>

<div class="container-fluid px-4">
    <div class="row mb-4">
        <div class="col">
            <h2 class="mt-4 mb-4"><i class="bi bi-pencil-square"></i> Edit Client</h2>
        </div>
    </div>

    <?php if (!empty($error)): ?>
        <div class="alert alert-danger">
            <?php echo htmlspecialchars($error); ?>
        </div>
    <?php endif; ?>

    <!-- Edit Client Form -->
    <div class="card shadow-sm">
        <div class="card-body">
            <form method="post" class="needs-validation" novalidate>
                <div class="row g-3">
                    <div class="col-12">
                        <label for="name" class="form-label">Client Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" required 
                               value="<?php echo htmlspecialchars($client['name']); ?>">
                        <div class="invalid-feedback">Please enter client name.</div>
                    </div>
                    
                    <div class="col-12">
                        <label for="bin" class="form-label">BIN</label>
                        <input type="text" class="form-control" id="bin" name="bin" 
                               value="<?php echo htmlspecialchars($client['bin'] ?? ''); ?>">
                    </div>
                    
                    <div class="col-12">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email" 
                               value="<?php echo htmlspecialchars($client['email'] ?? ''); ?>">
                        <div class="invalid-feedback">Please enter a valid email address.</div>
                    </div>
                    
                    <div class="col-12">
                        <label for="address" class="form-label">Address <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="address" name="address" rows="3" required><?php echo htmlspecialchars($client['address']); ?></textarea>
                        <div class="invalid-feedback">Please enter address.</div>
                    </div>
                </div>

                <div class="mt-4">
                    <button type="submit" name="edit_client" class="btn btn-primary">
                        <i class="bi bi-save me-2"></i>Save Changes
                    </button>
                    <a href="clients.php" class="btn btn-outline-secondary ms-2">
                        <i class="bi bi-x-circle me-2"></i>Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>