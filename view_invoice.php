<?php
// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

include 'config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Check if invoice ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: invoices.php');
    exit;
}

$invoice_id = $_GET['id'];

// Handle Add Payment form submission
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['add_payment'])) {
    $amount = $_POST['amount'];
    $payment_date = $_POST['payment_date'];
    $payment_method = $_POST['payment_method'];

    // Validate inputs
    if (!empty($amount) && !empty($payment_date) && !empty($payment_method)) {
        // Validate invoice belongs to user
        $checkInvoice = $pdo->prepare("SELECT id FROM invoices WHERE id = ? AND user_id = ?");
        $checkInvoice->execute([$invoice_id, $_SESSION['user_id']]);

        if ($checkInvoice->fetch()) {
            // Insert payment record
            $stmt = $pdo->prepare("INSERT INTO payments (invoice_id, amount, payment_date, payment_method) VALUES (?, ?, ?, ?)");

            if ($stmt->execute([$invoice_id, $amount, $payment_date, $payment_method])) {
                $payment_success = "Payment of $" . number_format($amount, 2) . " added successfully!";

                // Check if invoice is fully paid and update status
                $totalPaid = $pdo->prepare("SELECT SUM(amount) as total_paid FROM payments WHERE invoice_id = ?");
                $totalPaid->execute([$invoice_id]);
                $paidAmount = $totalPaid->fetch()['total_paid'] ?? 0;

                // Get invoice total
                $invoiceTotal = $pdo->prepare("SELECT
                    (SELECT SUM(quantity * unit_price) FROM invoice_items WHERE invoice_id = ?) as subtotal,
                    discount_rate, discount_fixed, vat_rate
                    FROM invoices WHERE id = ?");
                $invoiceTotal->execute([$invoice_id, $invoice_id]);
                $invoiceData = $invoiceTotal->fetch();

                $subtotal = $invoiceData['subtotal'] ?? 0;
                $discount = 0;
                if ($invoiceData['discount_rate']) {
                    $discount = $subtotal * ($invoiceData['discount_rate'] / 100);
                } elseif ($invoiceData['discount_fixed']) {
                    $discount = $invoiceData['discount_fixed'];
                }
                $afterDiscount = $subtotal - $discount;
                $vat = $afterDiscount * (($invoiceData['vat_rate'] ?? 0) / 100);
                $total = $afterDiscount + $vat;

                // Update payment status
                if ($paidAmount >= $total) {
                    $updateStatus = $pdo->prepare("UPDATE invoices SET payment_status = 'paid', paid_date = ? WHERE id = ?");
                    $updateStatus->execute([date('Y-m-d'), $invoice_id]);
                } else {
                    $updateStatus = $pdo->prepare("UPDATE invoices SET payment_status = 'processing' WHERE id = ?");
                    $updateStatus->execute([$invoice_id]);
                }
            } else {
                $payment_error = "Failed to add payment. Please try again.";
            }
        } else {
            $payment_error = "Invalid invoice.";
        }
    } else {
        $payment_error = "Please fill in all payment fields.";
    }
}

// Handle Delete Payment
if (isset($_GET['delete_payment']) && is_numeric($_GET['delete_payment'])) {
    $payment_id = $_GET['delete_payment'];

    // Verify the payment belongs to this invoice and user
    $checkPayment = $pdo->prepare("
        SELECT p.id
        FROM payments p
        JOIN invoices i ON p.invoice_id = i.id
        WHERE p.id = ? AND i.id = ? AND i.user_id = ?
    ");
    $checkPayment->execute([$payment_id, $invoice_id, $_SESSION['user_id']]);

    if ($checkPayment->fetch()) {
        $deletePayment = $pdo->prepare("DELETE FROM payments WHERE id = ?");
        if ($deletePayment->execute([$payment_id])) {
            $payment_success = "Payment deleted successfully!";

            // Recalculate payment status
            $totalPaid = $pdo->prepare("SELECT SUM(amount) as total_paid FROM payments WHERE invoice_id = ?");
            $totalPaid->execute([$invoice_id]);
            $paidAmount = $totalPaid->fetch()['total_paid'] ?? 0;

            if ($paidAmount == 0) {
                $updateStatus = $pdo->prepare("UPDATE invoices SET payment_status = 'unpaid', paid_date = NULL WHERE id = ?");
                $updateStatus->execute([$invoice_id]);
            } else {
                // Get invoice total to check if still fully paid
                $invoiceTotal = $pdo->prepare("SELECT
                    (SELECT SUM(quantity * unit_price) FROM invoice_items WHERE invoice_id = ?) as subtotal,
                    discount_rate, discount_fixed, vat_rate
                    FROM invoices WHERE id = ?");
                $invoiceTotal->execute([$invoice_id, $invoice_id]);
                $invoiceData = $invoiceTotal->fetch();

                $subtotal = $invoiceData['subtotal'] ?? 0;
                $discount = 0;
                if ($invoiceData['discount_rate']) {
                    $discount = $subtotal * ($invoiceData['discount_rate'] / 100);
                } elseif ($invoiceData['discount_fixed']) {
                    $discount = $invoiceData['discount_fixed'];
                }
                $afterDiscount = $subtotal - $discount;
                $vat = $afterDiscount * (($invoiceData['vat_rate'] ?? 0) / 100);
                $total = $afterDiscount + $vat;

                if ($paidAmount >= $total) {
                    $updateStatus = $pdo->prepare("UPDATE invoices SET payment_status = 'paid' WHERE id = ?");
                    $updateStatus->execute([$invoice_id]);
                } else {
                    $updateStatus = $pdo->prepare("UPDATE invoices SET payment_status = 'processing' WHERE id = ?");
                    $updateStatus->execute([$invoice_id]);
                }
            }

            // Redirect to remove the delete_payment parameter from URL
            header("Location: view_invoice.php?id=" . $invoice_id);
            exit;
        } else {
            $payment_error = "Failed to delete payment.";
        }
    } else {
        $payment_error = "Invalid payment.";
    }
}

// Handle Delete Payment
if (isset($_GET['delete_payment']) && is_numeric($_GET['delete_payment'])) {
    $payment_id = $_GET['delete_payment'];

    // Verify the payment belongs to this invoice and user, and get payment amount
    $checkPayment = $pdo->prepare("
        SELECT p.id, p.amount
        FROM payments p
        JOIN invoices i ON p.invoice_id = i.id
        WHERE p.id = ? AND i.id = ? AND i.user_id = ?
    ");
    $checkPayment->execute([$payment_id, $invoice_id, $_SESSION['user_id']]);
    $paymentData = $checkPayment->fetch();

    if ($paymentData) {
        $deletedAmount = $paymentData['amount'];
        $deletePayment = $pdo->prepare("DELETE FROM payments WHERE id = ?");
        if ($deletePayment->execute([$payment_id])) {
            // Redirect with success flag
            header("Location: view_invoice.php?id=" . $invoice_id . "&deleted=1&amount=" . urlencode(number_format($deletedAmount, 2)));
            exit;
        } else {
            $payment_error = "Failed to delete payment.";
        }
    } else {
        $payment_error = "Invalid payment.";
    }
}

// Handle payment deleted success message
if (isset($_GET['deleted']) && isset($_GET['amount'])) {
    $deletedAmount = urldecode($_GET['amount']);
    $payment_success = "Payment of $" . $deletedAmount . " deleted successfully!";
}

// Debug: Add a test success message to see if alerts work at all
if (isset($_GET['test_alert'])) {
    $payment_success = "Test alert is working!";
}

//*** file uploads scripts ***
// Configuration
$upload_dir = 'uploads/';

// Check if the form has been submitted
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['tds_vds_form'])) {
    // Validate invoice ID exists and belongs to the user
    $checkInvoice = $pdo->prepare("SELECT id FROM invoices WHERE id = ? AND user_id = ?");
    $checkInvoice->execute([$invoice_id, $_SESSION['user_id']]);
    if (!$checkInvoice->fetch()) {
        header('Location: invoices.php');
        exit;
    }

    // Define the files to process
    $filesToProcess = [
        'tds_certificate',
        'tds_challan',
        'vds_certificate',
        'vds_challan',
        'wo_document'
    ];

    foreach ($filesToProcess as $fileKey) {
        if (!empty($_FILES[$fileKey]['name'])) {
            $file = $_FILES[$fileKey];
            
            if ($file['error'] !== UPLOAD_ERR_OK) {
                echo '<div class="alert alert-danger text-center" role="alert">Error uploading ' . $fileKey . ': ' . getUploadError($file['error']) . '</div>';
                continue;
            }

            // Generate a unique filename
            $filename = uniqid() . '_' . basename($file['name']);
            $destination = $upload_dir . $filename;

            if (move_uploaded_file($file['tmp_name'], $destination)) {
                echo '<div class="alert alert-success text-center" role="alert">' . $fileKey . ' uploaded successfully.</div>';

                // Update the database
                $columnMapping = [
                    'tds_certificate' => 'tds_certificate',
                    'tds_challan' => 'tds_challan',
                    'vds_certificate' => 'vds_certificate',
                    'vds_challan' => 'vds_challan',
                    'wo_document' => 'wo_document'
                ];

                if (isset($columnMapping[$fileKey])) {
                    $stmt = $pdo->prepare("UPDATE invoices 
                                          SET {$columnMapping[$fileKey]} = ?, 
                                              uploaded_at = NOW() 
                                          WHERE id = ? AND user_id = ?");
                    $stmt->execute([$destination, $invoice_id, $_SESSION['user_id']]);
                    
                    if ($stmt->rowCount() === 0) {
                        echo '<div class="alert alert-danger text-center" role="alert">Failed to update database for ' . $fileKey . '</div>';
                    }
                }
            } else {
                echo '<div class="alert alert-danger text-center" role="alert">Failed to move ' . $fileKey . '.</div>';
            }
        }
    }
}

// Function to get the upload error message
function getUploadError($error) {
    switch ($error) {
        case UPLOAD_ERR_INI_SIZE:
            return 'The uploaded file exceeds the upload_max_filesize directive in php.ini';
        case UPLOAD_ERR_FORM_SIZE:
            return 'The uploaded file exceeds the MAX_FILE_SIZE directive that was specified in the HTML form';
        case UPLOAD_ERR_PARTIAL:
            return 'The uploaded file was only partially uploaded';
        case UPLOAD_ERR_NO_FILE:
            return 'No file was uploaded';
        case UPLOAD_ERR_NO_TMP_DIR:
            return 'Missing a temporary folder';
        case UPLOAD_ERR_CANT_WRITE:
            return 'Failed to write file to disk';
        case UPLOAD_ERR_EXTENSION:
            return 'A PHP extension stopped the file upload';
        default:
            return 'Unknown upload error';
    }
}
//*** file uploads scripts ends ***

// Get invoice with user access check
$stmt = $pdo->prepare("SELECT * FROM invoices 
                      JOIN clients ON invoices.client_id = clients.id
                      WHERE invoices.id = ? AND invoices.user_id = ?");
$stmt->execute([$invoice_id, $_SESSION['user_id']]);
$invoice = $stmt->fetch();

if (!$invoice) {
    header('Location: invoices.php');
    exit;
}

$page_title = "Invoice ID: KL-INV-" . $invoice_id;
include 'includes/header.php';

// Get invoice items
$stmt = $pdo->prepare("SELECT * FROM invoice_items 
                      JOIN items ON invoice_items.item_id = items.id
                      WHERE invoice_id = ?");
$stmt->execute([$invoice_id]);
$items = $stmt->fetchAll();

// Calculate Totals
$subtotal = 0;
foreach($items as $item){
    $subtotal += $item['unit_price'] * $item['quantity'];
}

$discount_rate = $subtotal * ($invoice['discount_rate'] / 100);
$discount_fixed = $invoice['discount_fixed'];
$total = $subtotal - ($discount_rate + $discount_fixed);
$tax = $total * ($invoice['vat_rate'] / 100);
$total_grand = $total + $tax;

$invoice_for = $invoice['invoice_for'];
$service_type = $invoice['service_type'];

// Format dates with null check
$invoice_date = !empty($invoice['invoice_date']) ? date("d M Y", strtotime($invoice['invoice_date'])) : 'Not Set';
$quote_date = !empty($invoice['quote_date']) ? date("d M Y", strtotime($invoice['quote_date'])) : 'Not Set';
$invoice_id_date = !empty($invoice['quote_date']) ? date("ymd", strtotime($invoice['quote_date'])) : date("ymd");
?>

<div id="my-pdf-content" class="card">
    <div class="card-body">
        <h6>Invoice ID: KL-INV-<?= $invoice_id_date ?>-<?= $invoice_id ?></h6>
        <h5 class="mb-0">Invoice Title: <?= $invoice_for ?></h5>
        <h6>Service Type: <?= $service_type ?></h6>
        <br>
        <div class="row mb-4">
            <div class="col-md-6">
                <p class="mb-0"><strong>Client:</strong> <?= $invoice['name'] ?></p>
                <p class="mb-0"><strong>Email:</strong> <?= $invoice['email'] ?></p>
                <p class="mb-0"><strong>Address:</strong> <?= $invoice['address'] ?></p>
            </div>
            <div class="col-md-6 text-end">
                <p class="mb-0"><strong>Quotation Date:</strong> <?= $quote_date ?></p>
                <p class="mb-0"><strong>Invoice Date:</strong> <?= $invoice_date ?></p>
                <p class="mb-0"><strong>Status:</strong> <?= ucfirst($invoice['payment_status']) ?></p>
            </div>
        </div>

        <table class="table table-bordered">
            <thead>
                <tr>
                    <th>Item</th>
                    <th>Description</th>
                    <th class="text-end">Unit Price</th>
                    <th class="text-center">Qty</th>
                    <th class="text-end">Price (BDT)</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach($items as $item): ?>
                <tr>
                    <td><?= $item['name'] ?></td>
                    <td><?= $item['summary'] ?></td>
                    <td class="text-end"><?= number_format($item['unit_price'], 2) ?></td>
                    <td class="text-center"><?= $item['quantity'] ?></td>
                    <td class="text-end"><?= number_format($item['unit_price'] * $item['quantity'], 2) ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>

        <div class="row">
            <div class="col-md-4 offset-md-8">
                <table class="table text-end">
                    <tr>
                        <th>Subtotal</th>
                        <td><?= number_format($subtotal, 2) ?></td>
                    </tr>

                    <?php if ($invoice['discount_rate'] > 0 || $invoice['discount_fixed'] > 0): ?>
                        <tr>
                            <th>Discount
                                <?php if ($invoice['discount_rate'] > 0): ?>
                                    (<?= $invoice['discount_rate'] ?>%)
                                <?php else: ?>
                                    (Onetime)
                                <?php endif; ?>
                                <div style="font-weight: normal; font-size: 13px; font-style: italic;"><?= $invoice['discount_notes'] ?></div>
                            </th>
                            <td>
                                <?php if ($invoice['discount_rate'] > 0): ?>
                                    -<?= number_format($discount_rate, 2) ?>
                                <?php else: ?>
                                    -<?= number_format($invoice['discount_fixed'], 2) ?>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <th>Total</th>
                            <td><?= number_format($total, 2) ?></td>
                        </tr>
                    <?php endif; ?>

                    <tr>
                        <th>VAT (<?= $invoice['vat_rate'] ?>%)</th>
                        <td><?= number_format($tax, 2) ?></td>
                    </tr>
                    <tr>
                        <th>Grand Total</th>
                        <td><?= number_format($total_grand, 2) ?></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

<br>
<hr>
<br>

<!-- Payment Messages -->
<?php if(isset($payment_success)): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle me-2"></i><?= $payment_success ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>
<?php if(isset($payment_error)): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle me-2"></i><?= $payment_error ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
<?php endif; ?>

<!-- Payment Form -->
<div class="card">
    <div class="card-header bg-success text-white">
        <h5 class="mb-0"><i class="bi bi-plus-circle me-2"></i>Add New Payment</h5>
    </div>
    <div class="card-body">
        <form method="post" class="mb-0">
            <div class="row g-3">
                <div class="col-md-3">
                    <label for="amount" class="form-label">Amount</label>
                    <input type="number" step="0.01" name="amount" id="amount" class="form-control" placeholder="0.00" required>
                </div>
                <div class="col-md-3">
                    <label for="payment_date" class="form-label">Payment Date</label>
                    <input type="date" name="payment_date" id="payment_date" class="form-control" value="<?= date('Y-m-d') ?>" required>
                </div>
                <div class="col-md-3">
                    <label for="payment_method" class="form-label">Payment Method</label>
                    <select name="payment_method" id="payment_method" class="form-select" required>
                        <option value="">Select Method</option>
                        <option value="Cash">Cash</option>
                        <option value="Credit Card">Credit Card</option>
                        <option value="Bank Transfer">Bank Transfer</option>
                        <option value="Check">Check</option>
                        <option value="Online Payment">Online Payment</option>
                    </select>
                </div>
                <div class="col-md-3 d-flex align-items-end">
                    <button type="submit" name="add_payment" class="btn btn-success w-100">
                        <i class="bi bi-plus-circle me-2"></i>Add Payment
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Payment History -->
<?php
// Get existing payments for this invoice
$paymentsQuery = $pdo->prepare("SELECT * FROM payments WHERE invoice_id = ? ORDER BY payment_date DESC");
$paymentsQuery->execute([$invoice_id]);
$payments = $paymentsQuery->fetchAll();

if (!empty($payments)):
?>
<div class="card mt-4">
    <div class="card-header bg-info text-dark">
        <h5 class="mb-0"><i class="bi bi-credit-card me-2"></i>Payment History</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Amount</th>
                        <th>Method</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach($payments as $payment): ?>
                    <tr>
                        <td><?= date('d M Y', strtotime($payment['payment_date'])) ?></td>
                        <td>$<?= number_format($payment['amount'], 2) ?></td>
                        <td>
                            <?php
                            $method_colors = [
                                'Cash' => 'bg-success',
                                'Credit Card' => 'bg-primary',
                                'Bank Transfer' => 'bg-info',
                                'Check' => 'bg-warning',
                                'Online Payment' => 'bg-secondary'
                            ];
                            $color = $method_colors[$payment['payment_method']] ?? 'bg-secondary';
                            ?>
                            <span class="badge <?= $color ?>"><?= htmlspecialchars($payment['payment_method']) ?></span>
                        </td>
                        <td>
                            <button type="button" class="btn btn-sm btn-outline-danger"
                                    data-bs-toggle="modal"
                                    data-bs-target="#deletePaymentModal"
                                    data-payment-id="<?= $payment['id'] ?>"
                                    data-payment-amount="<?= number_format($payment['amount'], 2) ?>"
                                    data-payment-date="<?= date('d M Y', strtotime($payment['payment_date'])) ?>"
                                    data-payment-method="<?= htmlspecialchars($payment['payment_method']) ?>">
                                <i class="bi bi-trash"></i> Delete
                            </button>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
                <tfoot>
                    <tr class="table-info">
                        <th>Total Paid:</th>
                        <th>$<?= number_format(array_sum(array_column($payments, 'amount')), 2) ?></th>
                        <th colspan="2"></th>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Payment Summary -->
<?php
// Calculate payment summary
$totalPaidQuery = $pdo->prepare("SELECT SUM(amount) as total_paid FROM payments WHERE invoice_id = ?");
$totalPaidQuery->execute([$invoice_id]);
$totalPaid = $totalPaidQuery->fetch()['total_paid'] ?? 0;

// Use the already calculated $total_grand from above
$invoiceTotal = $total_grand;
$remainingBalance = $invoiceTotal - $totalPaid;
?>

<div class="row mb-4 mt-4">
    <div class="col-md-4">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">Invoice Total</h5>
                <h3 class="text-primary">$<?= number_format($invoiceTotal, 2) ?></h3>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">Amount Paid</h5>
                <h3 class="text-success">$<?= number_format($totalPaid, 2) ?></h3>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title <?= $remainingBalance > 0 ? 'text-warning' : 'text-success' ?>">
                    <?= $remainingBalance > 0 ? 'Remaining Balance' : 'Fully Paid' ?>
                </h5>
                <h3 class="<?= $remainingBalance > 0 ? 'text-warning' : 'text-success' ?>">
                    $<?= number_format(max(0, $remainingBalance), 2) ?>
                </h3>
                <?php if ($remainingBalance <= 0): ?>
                    <span class="badge bg-success"><i class="bi bi-check-circle me-1"></i>Paid in Full</span>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<hr>

<!-- Document Download Buttons -->
<div class="mb-4">
    <a href="view_pdf_new.php?id=<?= $invoice_id ?>" class="btn btn-lg btn-primary mb-2">Invoice</a>
    <a href="view_pdf_new_sign.php?id=<?= $invoice_id ?>" class="btn btn-lg btn-primary mb-2">Invoice (/w Signature)</a>
    <a href="view_pdf_quote.php?id=<?= $invoice_id ?>" class="btn btn-lg btn-primary mb-2">Quotation</a>
    <a href="view_pdf_quote_vat.php?id=<?= $invoice_id ?>" class="btn btn-lg btn-primary mb-2">Quotation (/w VAT)</a>
    <a href="view_challan.php?id=<?= $invoice_id ?>" class="btn btn-lg btn-primary mb-2">Challan</a>
    <a href="view_receipt.php?id=<?= $invoice_id ?>" class="btn btn-lg btn-primary mb-2">Receipt</a>
    <a href="view_tax_exempt.php?id=<?= $invoice_id ?>" class="btn btn-lg btn-primary mb-2">TAX Exempt Certificate</a>
    <a href="edit_invoice_new.php?id=<?= $invoice_id ?>" class="btn btn-lg btn-danger mb-2">Edit</a>
</div>

<hr>

<!-- Document Upload Form -->
<form method="post" enctype="multipart/form-data">
    <input type="hidden" name="tds_vds_form" value="1">
    <div class="row">
        <div class="row mt-3">
            <div class="col-md-3">
                <label for="wo_document">Work Order (WO):</label>
                <input type="file" name="wo_document" id="wo_document" class="form-control">
            </div>
        </div>
    </div>
    <hr>
    <div class="row">
        <div class="col-md-3">
            <label for="tds_certificate">TDS Certificate:</label>
            <input type="file" name="tds_certificate" id="tds_certificate" class="form-control">
        </div>
        <div class="col-md-3">
            <label for="tds_challan">TDS Challan:</label>
            <input type="file" name="tds_challan" id="tds_challan" class="form-control">
        </div>
        <div class="col-md-3">
            <label for="vds_certificate">VDS Certificate:</label>
            <input type="file" name="vds_certificate" id="vds_certificate" class="form-control">
        </div>
        <div class="col-md-3">
            <label for="vds_challan">VDS Challan:</label>
            <input type="file" name="vds_challan" id="vds_challan" class="form-control">
        </div>
    </div>
    <br>
    <button type="submit" class="btn btn-primary">Submit</button>
</form>

<hr>

<!-- Download WO Section -->
<h3>Download WO</h3>
<div class="row">
    <?php if (!empty($invoice['wo_document'])): ?>
    <div class="col-md-3">
        <label>Work Order:</label>
        <a class="btn btn-sm btn-primary" href="<?= $invoice['wo_document'] ?>">Download</a>
    </div>
    <?php endif; ?>
</div>

<hr>

<!-- Download TDS & VDS Section -->
<h3>Download TDS & VDS</h3>
<div class="row">
    <?php if (!empty($invoice['tds_certificate'])): ?>
    <div class="col-md-3">
        <label>TDS Certificate:</label>
        <a class="btn btn-sm btn-primary" href="<?= $invoice['tds_certificate'] ?>">Download</a>
    </div>
    <?php endif; ?>
    <?php if (!empty($invoice['tds_challan'])): ?>
    <div class="col-md-3">
        <label>TDS Challan:</label>
        <a class="btn btn-sm btn-primary" href="<?= $invoice['tds_challan'] ?>">Download</a>
    </div>
    <?php endif; ?>
    <?php if (!empty($invoice['vds_certificate'])): ?>
    <div class="col-md-3">
        <label>VDS Certificate:</label>
        <a class="btn btn-sm btn-primary" href="<?= $invoice['vds_certificate'] ?>">Download</a>
    </div>
    <?php endif; ?>
    <?php if (!empty($invoice['vds_challan'])): ?>
    <div class="col-md-3">
        <label>VDS Challan:</label>
        <a class="btn btn-sm btn-primary" href="<?= $invoice['vds_challan'] ?>">Download</a>
    </div>
    <?php endif; ?>
</div>

<hr>

<!-- Delete Payment Modal -->
<div class="modal fade" id="deletePaymentModal" tabindex="-1" aria-labelledby="deletePaymentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deletePaymentModalLabel">
                    <i class="bi bi-exclamation-triangle me-2"></i>Delete Payment
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-3">
                    <i class="bi bi-trash text-danger" style="font-size: 3rem;"></i>
                </div>
                <p class="text-center mb-3">Are you sure you want to delete this payment?</p>
                <div class="card bg-light">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6"><strong>Amount:</strong></div>
                            <div class="col-6" id="modal-payment-amount">-</div>
                        </div>
                        <div class="row">
                            <div class="col-6"><strong>Date:</strong></div>
                            <div class="col-6" id="modal-payment-date">-</div>
                        </div>
                        <div class="row">
                            <div class="col-6"><strong>Method:</strong></div>
                            <div class="col-6" id="modal-payment-method">-</div>
                        </div>
                    </div>
                </div>
                <div class="alert alert-warning mt-3">
                    <i class="bi bi-info-circle me-2"></i>
                    <strong>Warning:</strong> This action cannot be undone. The payment will be permanently removed and the invoice status may be updated.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle me-2"></i>Cancel
                </button>
                <a href="#" id="confirmDeleteBtn" class="btn btn-danger">
                    <i class="bi bi-trash me-2"></i>Delete Payment
                </a>
            </div>
        </div>
    </div>
</div>

<script>
// Handle delete payment modal
document.addEventListener('DOMContentLoaded', function() {
    const deletePaymentModal = document.getElementById('deletePaymentModal');
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');

    deletePaymentModal.addEventListener('show.bs.modal', function(event) {
        const button = event.relatedTarget;
        const paymentId = button.getAttribute('data-payment-id');
        const paymentAmount = button.getAttribute('data-payment-amount');
        const paymentDate = button.getAttribute('data-payment-date');
        const paymentMethod = button.getAttribute('data-payment-method');

        // Update modal content
        document.getElementById('modal-payment-amount').textContent = '$' + paymentAmount;
        document.getElementById('modal-payment-date').textContent = paymentDate;
        document.getElementById('modal-payment-method').innerHTML = '<span class="badge bg-secondary">' + paymentMethod + '</span>';

        // Update delete link
        confirmDeleteBtn.href = '?id=<?= $invoice_id ?>&delete_payment=' + paymentId;
    });
});
</script>

<?php include 'includes/footer.php'; ?>
