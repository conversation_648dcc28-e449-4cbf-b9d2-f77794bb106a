-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.2
-- https://www.phpmyadmin.net/
--
-- Host: localhost
-- Generation Time: Jul 01, 2025 at 05:59 AM
-- Server version: 9.3.0
-- PHP Version: 8.2.28

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `invoice_system_sas`
--

-- --------------------------------------------------------

--
-- Table structure for table `clients`
--

CREATE TABLE `clients` (
  `id` int NOT NULL,
  `bin` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `address` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `user_id` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `clients`
--

INSERT INTO `clients` (`id`, `bin`, `name`, `email`, `address`, `user_id`) VALUES
(1, '000000169-0005', 'Incepta Pharmaceuticals Ltd.', '', '40, Shaheed Tajuddin Ahmed Ave, Tejgaon I/A, Dhaka 1208', 1),
(2, '000377878-0101', 'UNIMART LIMITED', '', 'Gulshan Centre Point, House 23-26, Road 90, Gulshan 2', 1),
(4, '', 'IPDI Foundation', '<EMAIL>', '21/2 Pallabi, Mirpur 12, Dhaka-1216', 1),
(5, '', 'Attest Design Ltd.', '<EMAIL>', '26/2 Central Road, Ground Floor, New Market, Dhaka-1205', 1),
(6, '', 'United Group', '', 'United House, Madani Avenue, United City, Dhaka-1212', 1),
(8, '004004348-0101', 'United Healthcare Services Ltd', '', 'Plot# 107,2524,2527, United City, Madani Avenue, Dhaka-1212', 1),
(9, '006500468-0102', 'Webbly Solutions', '<EMAIL>', 'Plot 22, Road 17, Sector 13, Uttara, Dhaka', 1),
(10, '*********-0101', 'Medipac Technologies Limited', '', 'United House, Madani Avenue, United City, Dhaka-1212', 1),
(11, '', 'Alokito Teachers', '<EMAIL>', 'House 127, Road 1, Avenue 3, Mirpur DOHS', 1),
(12, '', 'IPCO Developments (Bangladesh) Ltd.', '', 'Dhaka Tongi Highway, Kurmitola, Dhaka', 1),
(13, '', 'Alokito Hridoy', '<EMAIL>', 'House 127, Road 1, Avenue 3, Mirpur DOHS', 1),
(14, '', 'Loren D. Stark Company (LDSCO)', '<EMAIL>', 'Houston, TX 77099, USA', 1),
(15, '*********-0101', 'United Enterprises &amp; Co. Ltd.', '', 'Gulshan Centre Point, H # 23-26, R # 90,91, Gulshan 2, Dhaka-1212', 1),
(16, '', 'Bangladesh Medical Solutions Pvt. Ltd.', '', 'Uttara, Dhaka', 1);

-- --------------------------------------------------------

--
-- Table structure for table `email_verification_tokens`
--

CREATE TABLE `email_verification_tokens` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `token` varchar(255) NOT NULL,
  `expires_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

--
-- Dumping data for table `email_verification_tokens`
--

INSERT INTO `email_verification_tokens` (`id`, `user_id`, `token`, `expires_at`, `created_at`) VALUES
(1, 1, '995c28a5f493367097fbc5cf36aadfa157120c4028216988bc683f7c538a9f29', '2025-06-07 13:30:23', '2025-06-07 18:30:23');

-- --------------------------------------------------------

--
-- Table structure for table `invoices`
--

CREATE TABLE `invoices` (
  `id` int NOT NULL,
  `client_id` int DEFAULT NULL,
  `invoice_for` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `service_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `invoice_date` date DEFAULT NULL,
  `quote_date` date DEFAULT NULL,
  `paid_date` date DEFAULT NULL,
  `discount_rate` decimal(5,2) DEFAULT NULL,
  `discount_fixed` decimal(10,2) DEFAULT NULL,
  `discount_notes` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `vat_rate` decimal(5,2) DEFAULT NULL,
  `tax_exempt` enum('yes','no') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `payment_status` enum('paid','unpaid','processing') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `wo_document` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `tds_certificate` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `tds_challan` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `vds_certificate` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `vds_challan` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `uploaded_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `user_id` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `invoices`
--

INSERT INTO `invoices` (`id`, `client_id`, `invoice_for`, `service_type`, `invoice_date`, `quote_date`, `paid_date`, `discount_rate`, `discount_fixed`, `discount_notes`, `vat_rate`, `tax_exempt`, `payment_status`, `wo_document`, `tds_certificate`, `tds_challan`, `vds_certificate`, `vds_challan`, `uploaded_at`, `user_id`) VALUES
(6, 4, 'HeLO-IPDI Website (January 2025)', 'Website Maintenance', '2025-02-12', '2025-02-08', NULL, 0.00, 2400.00, 'Valid till May 2025', 0.00, 'no', 'paid', NULL, NULL, NULL, NULL, NULL, '2025-02-15 21:16:16', 1),
(7, 1, 'Professor Dr. S M Mustafa Zaman', 'Website Service Renewal', '2025-02-18', '2024-10-09', NULL, 0.00, 0.00, '', 5.00, 'yes', 'paid', NULL, NULL, NULL, NULL, NULL, '2025-02-15 21:16:16', 1),
(14, 5, 'Website Development & Maintenance', 'Website Services', '2025-01-30', '2025-01-30', NULL, 0.00, 200000.00, 'Advanced Payment Adjustment', 0.00, 'yes', 'paid', NULL, NULL, NULL, NULL, NULL, '2025-02-15 21:16:16', 1),
(138, 6, 'Domain Renewal for meghbon.com', 'Web Services', '2025-02-15', '2025-03-02', NULL, 0.00, 269.00, '', 15.00, 'no', 'processing', NULL, NULL, NULL, NULL, NULL, '2025-02-15 22:04:10', 1),
(142, 6, 'Domain Registration for playardbd.com', 'Web Services', '2025-03-05', '2025-02-18', NULL, 0.00, 0.00, '', 15.00, 'no', 'unpaid', NULL, NULL, NULL, NULL, NULL, '2025-02-17 19:09:15', 1),
(143, 6, 'Domain Registration for crowneplazadhaka.com ', 'Web Services', '2025-03-10', '2025-03-23', NULL, 0.00, 0.00, '', 15.00, 'no', 'unpaid', NULL, NULL, NULL, NULL, NULL, '2025-02-23 21:50:33', 1),
(144, 1, 'Website Development for CKM & KDRG', 'Web Services', '2025-02-24', '2025-01-15', '2025-05-25', 0.00, 0.00, '', 5.00, 'yes', 'paid', NULL, NULL, NULL, NULL, NULL, '2025-02-24 14:33:39', 1),
(145, 8, 'Domain Renewal for uhslbd.com ', 'Web Services', '2025-04-07', '2025-02-26', NULL, 0.00, 0.00, '', 15.00, 'no', 'processing', 'uploads/683a0b4e2652a_wo.pdf', NULL, NULL, NULL, NULL, '2025-05-31 01:47:26', 1),
(146, 9, 'UI & Frontend for Proptinize', 'Web Services', NULL, '2025-02-26', NULL, 0.00, 0.00, '', 0.00, 'no', 'unpaid', NULL, NULL, NULL, NULL, NULL, '2025-02-26 15:48:02', 1),
(147, 9, 'Design & Development for Bangladesh Unbound', 'Web Services', NULL, '2025-02-26', NULL, 0.00, 0.00, '', 0.00, 'no', 'unpaid', NULL, NULL, NULL, NULL, NULL, '2025-02-26 15:49:46', 1),
(149, 4, 'HeLO-IPDI Website (February 2025)', 'Website Maintenance', '2025-03-18', '2025-03-04', NULL, 0.00, 2400.00, 'Valid till May 2025', 0.00, 'no', 'paid', NULL, NULL, NULL, NULL, NULL, '2025-03-04 03:11:06', 1),
(150, 1, 'OPSSA Website Maintenance', 'Web Services', '2025-03-11', '2025-03-10', NULL, 0.00, 0.00, '', 5.00, 'yes', 'paid', NULL, NULL, NULL, NULL, NULL, '2025-03-10 13:36:13', 1),
(151, 10, 'Domain Registration for techvitalbd.com', 'Web Services', '2025-03-23', '2025-03-13', '2025-05-12', 0.00, 0.00, '', 15.00, 'no', 'paid', NULL, NULL, NULL, NULL, NULL, '2025-03-13 13:45:20', 1),
(152, 6, 'Domain Registration for unitedlubeoilltd.com', 'Web Services', '2025-04-01', '2025-03-18', NULL, 0.00, 0.00, '', 15.00, 'no', 'unpaid', NULL, NULL, NULL, NULL, NULL, '2025-03-17 14:01:08', 1),
(153, 12, 'Domain & Hosting for cpdhakaairport.com', 'Cloud Services', '2025-04-06', '2025-03-24', '2025-05-20', 0.00, 0.00, '', 15.00, 'no', 'paid', NULL, NULL, NULL, NULL, NULL, '2025-03-20 10:57:04', 1),
(154, 11, 'Domain Registration for alokitochatbot.com', 'Web Services', '2025-04-04', '2025-03-20', NULL, 0.00, 0.00, '', 0.00, 'no', 'paid', NULL, NULL, NULL, NULL, NULL, '2025-03-20 11:06:43', 1),
(157, 6, 'Domain Registration & SSL Certificate for wellbeinguphl.com', 'Web Services', '2025-04-29', '2025-04-15', NULL, 0.00, 0.00, '', 15.00, 'yes', 'unpaid', NULL, NULL, NULL, NULL, NULL, '2025-04-14 13:41:51', 1),
(158, 11, 'Domain Renewal for alokitoteachers.com', 'Web Services', '2025-05-04', '2025-04-19', '2025-05-19', 10.00, 0.00, 'VTD/TDS', 0.00, 'no', 'paid', NULL, NULL, NULL, NULL, NULL, '2025-04-19 11:25:02', 1),
(159, 6, 'Domain Registration for ulolbd.com', 'Web Services', NULL, '2025-05-04', NULL, 0.00, 0.00, '', 15.00, 'no', 'unpaid', NULL, NULL, NULL, NULL, NULL, '2025-05-03 13:02:59', 1),
(160, 4, 'HeLO-IPDI Website (March-April 2025)', 'Website Maintenance', '2025-05-11', '2025-05-06', '2025-05-13', 0.00, 4800.00, 'Valid till May 2025', 0.00, 'no', 'paid', NULL, NULL, NULL, NULL, NULL, '2025-05-05 20:43:19', 1),
(161, 13, 'Domain Renewal for alokitohridoy.org', 'Web Services', '2025-05-18', '2025-05-18', '2025-05-19', 10.00, 0.00, 'VTD/TDS', 0.00, 'no', 'paid', NULL, NULL, NULL, NULL, NULL, '2025-05-18 10:05:45', 1),
(163, 1, 'NINS Patient Database Application', 'Website Services', '2025-05-20', '2025-05-19', NULL, 0.00, 0.00, '', 5.00, 'yes', 'processing', NULL, NULL, NULL, NULL, NULL, '2025-05-19 20:21:27', 1),
(164, 15, 'Website Maintenance for United Group', 'Web Services', '2025-06-01', '2025-05-22', NULL, 0.00, 20000.00, '', 5.00, 'yes', 'processing', 'uploads/6844296497a29_Klay_Technologies_PO_2740_UECL.pdf', NULL, NULL, NULL, NULL, '2025-06-07 17:58:28', 1),
(165, 6, 'SSL Certificate (CA-Signed)', 'Web Services', NULL, '2025-05-27', NULL, 0.00, 0.00, '', 15.00, 'no', 'unpaid', NULL, NULL, NULL, NULL, NULL, '2025-05-27 12:51:30', 1),
(166, 14, 'Social Media Page Management', 'Social Media', '2025-06-02', '2025-05-30', '2025-06-02', 0.00, 0.00, '', 0.00, 'no', 'paid', NULL, NULL, NULL, NULL, NULL, '2025-05-30 21:37:20', 1),
(169, 5, 'Server & Email Services', 'Cloud Services', '2025-05-10', '2025-05-10', '2025-05-12', 0.00, 0.00, '', 0.00, 'no', 'paid', NULL, NULL, NULL, NULL, NULL, '2025-05-31 03:28:16', 1),
(172, 4, 'HeLO-IPDI Website (May 2025)', 'Website Maintenance', '2025-06-02', '2025-06-02', '2025-06-04', 0.00, 2400.00, 'Valid till May 2025', 0.00, 'no', 'paid', NULL, NULL, NULL, NULL, NULL, '2025-06-02 13:54:22', 1),
(173, 16, 'Medical Report Management Application', 'Website Services', '2025-06-22', '2025-06-04', NULL, 0.00, 100000.00, '', 0.00, 'yes', 'unpaid', NULL, NULL, NULL, NULL, NULL, '2025-06-04 20:38:45', 1);

-- --------------------------------------------------------

--
-- Table structure for table `invoice_items`
--

CREATE TABLE `invoice_items` (
  `id` int NOT NULL,
  `invoice_id` int DEFAULT NULL,
  `item_id` int DEFAULT NULL,
  `special_notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `quantity` int DEFAULT NULL,
  `unit_price` decimal(10,2) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `invoice_items`
--

INSERT INTO `invoice_items` (`id`, `invoice_id`, `item_id`, `special_notes`, `quantity`, `unit_price`) VALUES
(97, 142, 1, 'playardbd.com registration including service charges', 2, 2625.00),
(98, 6, 6, 'For the Month of January 2025', 1, 11900.00),
(103, 14, 8, 'Advanced Payment Adjusted in the Total', 1, 550000.00),
(104, 14, 9, 'AWS Server, Storage, CDN & SMTP', 3, 18000.00),
(124, 138, 1, 'meghbon.com renewal including service charges', 2, 2625.00),
(147, 152, 1, 'unitedlubeoilltd.com registration including service charges', 2, 2625.00),
(149, 149, 6, 'For the Month of February 2025', 1, 11900.00),
(171, 143, 1, 'crowneplazadhaka.com registration including service charges', 3, 2625.00),
(183, 157, 1, 'wellbeinguphl.com registration including service charges', 3, 2625.00),
(184, 157, 15, 'SSL Certificate for wellbeinguphl.com', 3, 2250.00),
(188, 150, 14, '', 1, 6000.00),
(189, 150, 1, 'opssa.org including privacy protection', 1, 2950.00),
(190, 150, 6, '', 12, 5000.00),
(191, 154, 1, 'alokitochatbot.com registration including service charges', 1, 2100.00),
(222, 7, 3, 'Domain: mustafazaman.org', 1, 2300.00),
(223, 7, 2, 'Domain: mustafazaman.org', 1, 650.00),
(224, 7, 6, '', 12, 5000.00),
(225, 7, 7, '', 1, 4000.00),
(230, 165, 15, '1 Domain - with/without www only (Including service charges)', 2, 2600.00),
(235, 145, 1, 'uhslbd.com domain renewal for 2 years', 2, 2625.00),
(243, 163, 8, 'UI/UX Design, Frontend Development, Backend Development & Features-wise Development', 1, 528000.00),
(244, 163, 6, 'Server & Maintenance', 12, 10125.00),
(245, 163, 3, 'ninsbd.org registration including Privacy Protection', 1, 2950.00),
(257, 169, 18, 'Google Workspace (Business Starter) Monthly Payment for 3 Months @1,090/m for 3 Users Accounts (May-July 2025)', 1, 9810.00),
(258, 169, 18, 'AWS Server, Storage, CDN & STMP Monthly Payment for 3 Months @18,000/m (May-July 2025)', 1, 54000.00),
(259, 160, 6, 'For the Month of March-April 2025', 1, 23800.00),
(260, 144, 8, 'CKM Website Development', 1, 76750.00),
(261, 144, 6, 'KDRG Website Maintenance for 12 Months', 1, 30000.00),
(264, 151, 1, 'techvitalbd.com registration including service charges', 2, 2625.00),
(265, 161, 3, 'alokitohridoy.org', 1, 2300.00),
(266, 158, 1, 'alokitoteachers.com', 1, 2100.00),
(267, 153, 1, 'cpdhakaairport.com registration including service charges', 3, 2625.00),
(268, 153, 7, '', 3, 4000.00),
(269, 159, 1, 'ulolbd.com registration including service charges', 2, 2625.00),
(271, 164, 16, 'For United Group - united.com.bd', 12, 10000.00),
(280, 166, 17, 'Post content creation, design & page management', 1, 32890.00),
(283, 146, 5, 'UI Design Only', 1, 50000.00),
(284, 146, 10, '', 1, 25000.00),
(285, 147, 5, '', 1, 100000.00),
(286, 147, 10, '', 1, 25000.00),
(287, 147, 11, '', 1, 120000.00),
(289, 172, 6, 'For the Month of May 2025', 1, 11900.00),
(299, 173, 8, 'Medical report web application for hospitals and doctors to manage medical reports for X-Ray, CT-Scan, and MRI', 1, 750000.00);

-- --------------------------------------------------------

--
-- Table structure for table `items`
--

CREATE TABLE `items` (
  `id` int NOT NULL,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `summary` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `price` decimal(10,2) DEFAULT NULL,
  `user_id` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `items`
--

INSERT INTO `items` (`id`, `name`, `summary`, `description`, `price`, `user_id`) VALUES
(1, '.com Domain', 'Yearly', 'Domain registration/renewal on an annual basis.', 2100.00, 1),
(2, 'Domain Privacy Protection', 'Yearly', 'Protection of domain ownership details, renewed annually.', 650.00, 1),
(3, '.org Domain', 'Yearly', 'Domain registration/renewal on an annual basis.', 2300.00, 1),
(4, '.net Domain', 'Yearly', 'Domain registration/renewal on an annual basis.', 2300.00, 1),
(5, 'Website Design (UI/UX)', 'One-time', 'Creation of a visually appealing and user-friendly user interface design, ensuring a visually engaging website layout and aesthetics', 80000.00, 1),
(6, 'Website Maintenance', 'Monthly', 'Website & plugins/extensions updates, server management, security, bug fix, etc.', 5000.00, 1),
(7, 'Hosting 5GB SSD Pro', 'Yearly', '5GB SSD Space, 2GB RAM, Unlimited Bandwidth, Free SSL Certificates, SSH Access, Daily Offsite Backup', 4000.00, 1),
(8, 'Website Development', 'One-time', 'Implementation of the website\'s frontend using HTML, CSS, JavaScript, and other technologies.', 250000.00, 1),
(9, 'Web Services', 'Monthly', 'DevOps, SMTP, etc.', 5000.00, 1),
(10, 'Frontend Development', 'One-time	', 'Implementation of the website\'s frontend using HTML, CSS, JavaScript, and other technologies.', 50000.00, 1),
(11, 'Backend Development', 'One-time', 'Development of the website\'s backend functionality, databases, and server-side logic.', 75000.00, 1),
(12, 'Responsive Design', 'One-time', 'Adaptive to different screen sizes and devices, ensuring a seamless browsing experience for users', 10000.00, 1),
(13, 'Logo Design', 'One-time', '2 initial concepts, 2 rounds of revisions, final delivery in multiple formats', 25000.00, 1),
(14, 'Hosting 10GB SSD Pro', 'Yearly', '10GB SSD Space, 2GB RAM, Unlimited Bandwidth, Free SSL Certificates, SSH Access, Daily Offsite Backup', 6000.00, 1),
(15, 'SSL Certificate', 'Yearly', 'SSL Certificate for secure website encryption and data protection.', 2250.00, 1),
(16, 'Website Maintenance', 'Monthly', 'Website and plugin/extension updates, content changes without major template customization and bug fixes.', 10000.00, 1),
(17, 'Social Media', 'Monthly', 'Post content creation, design & page management', 25000.00, 1),
(18, 'Others', 'N/A', 'Others services', 0.00, 1);

-- --------------------------------------------------------

--
-- Table structure for table `password_reset_tokens`
--

CREATE TABLE `password_reset_tokens` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `token` varchar(255) NOT NULL,
  `expires_at` datetime NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Dumping data for table `password_reset_tokens`
--

INSERT INTO `password_reset_tokens` (`id`, `user_id`, `token`, `expires_at`, `created_at`) VALUES
(9, 5, '99c28f53e66e18db86eb646a3d7995a28fd3e19b901317e7a535de54432362fb', '2025-06-07 19:49:39', '2025-06-07 18:49:39'),
(13, 5, 'b21622a7ef636dc97fedbb5a924f1beacd613253c285b0820261517c0e76c30d', '2025-06-08 06:35:36', '2025-06-08 05:35:36'),
(14, 5, '34173ede7b943f97315009dcf7a515c59048fef712af002dc84f81a5d2c34473', '2025-06-08 06:40:14', '2025-06-08 05:40:14');

-- --------------------------------------------------------

--
-- Table structure for table `payments`
--

CREATE TABLE `payments` (
  `id` int NOT NULL,
  `invoice_id` int DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  `payment_date` date DEFAULT NULL,
  `payment_method` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` int NOT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `full_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `company_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `company_address` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `company_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `company_email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `company_website` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `company_logo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `email_verified` tinyint(1) DEFAULT '0',
  `verification_token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `email`, `password`, `created_at`, `full_name`, `company_name`, `company_address`, `company_phone`, `company_email`, `company_website`, `company_logo`, `email_verified`, `verification_token`, `updated_at`) VALUES
(1, '<EMAIL>', '$2y$10$4uECfHhgdDDI4XiJxPeMNuIbE1lrv2r9pHaYEHjIizA9Pu4dpwcCC', '2025-05-31 14:09:53', 'Nasim Ahmed', 'Klay Technologies', 'House# 460/A, Road# 6, Avenue# 7, Mirpur DOHS, Dhaka, Bangladesh', '8801552454543', '<EMAIL>', 'https://klay.tech', NULL, 1, NULL, '2025-06-08 06:25:24'),
(5, '<EMAIL>', '$2y$10$dURrMeKiZzaKP.qaUA7CV.b8YYE02nUxlexMwXH4bxjpqmHA86lqC', '2025-06-07 07:09:59', 'Tareef Ubayd', 'Tareef Inc', NULL, NULL, NULL, NULL, NULL, 1, '2288f02076f63069ad1e943bc855c0c17b1343c29dc634b6a7bb193279dc1201', '2025-06-07 19:56:20');

-- --------------------------------------------------------

--
-- Table structure for table `user_settings`
--

CREATE TABLE `user_settings` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

--
-- Dumping data for table `user_settings`
--

INSERT INTO `user_settings` (`id`, `user_id`, `setting_key`, `setting_value`, `created_at`, `updated_at`) VALUES
(1, 1, 'invoice_prefix', 'KL-INV-', '2025-06-07 12:52:04', '2025-06-07 12:52:04'),
(2, 1, 'default_vat_rate', '5.00', '2025-06-07 12:52:04', '2025-06-07 12:52:04'),
(3, 1, 'invoice_footer_text', 'Thank you for your business!', '2025-06-07 12:52:04', '2025-06-07 12:52:04'),
(6, 5, 'invoice_prefix', 'INV-', '2025-06-07 13:09:59', '2025-06-07 13:09:59'),
(7, 5, 'default_vat_rate', '5.00', '2025-06-07 13:09:59', '2025-06-07 13:09:59'),
(8, 5, 'invoice_footer_text', 'Thank you for your business!', '2025-06-07 13:09:59', '2025-06-07 13:09:59'),
(9, 5, 'currency_symbol', 'BDT', '2025-06-07 13:09:59', '2025-06-07 13:09:59'),
(10, 5, 'date_format', 'd M Y', '2025-06-07 13:10:00', '2025-06-07 13:10:00'),
(14, 1, 'currency_symbol', 'BDT', '2025-06-07 13:13:44', '2025-06-07 13:13:44'),
(17, 1, 'date_format', 'd M Y', '2025-06-07 13:13:44', '2025-06-07 13:13:44');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `clients`
--
ALTER TABLE `clients`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_clients_user_id` (`user_id`);

--
-- Indexes for table `email_verification_tokens`
--
ALTER TABLE `email_verification_tokens`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_token` (`token`),
  ADD KEY `idx_verification_tokens_user_id` (`user_id`);

--
-- Indexes for table `invoices`
--
ALTER TABLE `invoices`
  ADD PRIMARY KEY (`id`),
  ADD KEY `client_id` (`client_id`),
  ADD KEY `idx_invoices_user_id` (`user_id`);

--
-- Indexes for table `invoice_items`
--
ALTER TABLE `invoice_items`
  ADD PRIMARY KEY (`id`),
  ADD KEY `invoice_id` (`invoice_id`),
  ADD KEY `item_id` (`item_id`);

--
-- Indexes for table `items`
--
ALTER TABLE `items`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_items_user_id` (`user_id`);

--
-- Indexes for table `password_reset_tokens`
--
ALTER TABLE `password_reset_tokens`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `token` (`token`),
  ADD KEY `idx_token` (`token`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_expires_at` (`expires_at`);

--
-- Indexes for table `payments`
--
ALTER TABLE `payments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `invoice_id` (`invoice_id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`),
  ADD UNIQUE KEY `unique_email` (`email`);

--
-- Indexes for table `user_settings`
--
ALTER TABLE `user_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_setting` (`user_id`,`setting_key`),
  ADD KEY `idx_user_settings_user_id` (`user_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `clients`
--
ALTER TABLE `clients`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=20;

--
-- AUTO_INCREMENT for table `email_verification_tokens`
--
ALTER TABLE `email_verification_tokens`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `invoices`
--
ALTER TABLE `invoices`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=177;

--
-- AUTO_INCREMENT for table `invoice_items`
--
ALTER TABLE `invoice_items`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=300;

--
-- AUTO_INCREMENT for table `items`
--
ALTER TABLE `items`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=21;

--
-- AUTO_INCREMENT for table `password_reset_tokens`
--
ALTER TABLE `password_reset_tokens`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT for table `payments`
--
ALTER TABLE `payments`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `user_settings`
--
ALTER TABLE `user_settings`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=40;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `clients`
--
ALTER TABLE `clients`
  ADD CONSTRAINT `fk_clients_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `email_verification_tokens`
--
ALTER TABLE `email_verification_tokens`
  ADD CONSTRAINT `email_verification_tokens_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `invoices`
--
ALTER TABLE `invoices`
  ADD CONSTRAINT `fk_invoices_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `invoices_ibfk_1` FOREIGN KEY (`client_id`) REFERENCES `clients` (`id`);

--
-- Constraints for table `invoice_items`
--
ALTER TABLE `invoice_items`
  ADD CONSTRAINT `invoice_items_ibfk_1` FOREIGN KEY (`invoice_id`) REFERENCES `invoices` (`id`),
  ADD CONSTRAINT `invoice_items_ibfk_2` FOREIGN KEY (`item_id`) REFERENCES `items` (`id`);

--
-- Constraints for table `items`
--
ALTER TABLE `items`
  ADD CONSTRAINT `fk_items_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `password_reset_tokens`
--
ALTER TABLE `password_reset_tokens`
  ADD CONSTRAINT `password_reset_tokens_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `payments`
--
ALTER TABLE `payments`
  ADD CONSTRAINT `payments_ibfk_1` FOREIGN KEY (`invoice_id`) REFERENCES `invoices` (`id`);

--
-- Constraints for table `user_settings`
--
ALTER TABLE `user_settings`
  ADD CONSTRAINT `user_settings_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
