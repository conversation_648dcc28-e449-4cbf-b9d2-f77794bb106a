<?php
// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

include 'config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Check if invoice ID is provided
if (!isset($_POST['id']) || !is_numeric($_POST['id'])) {
    header('Location: invoices.php');
    exit;
}

$invoice_id = $_POST['id'];

// Verify invoice belongs to user before deletion
$stmt = $pdo->prepare("SELECT id FROM invoices WHERE id = ? AND user_id = ?");
$stmt->execute([$invoice_id, $_SESSION['user_id']]);
if (!$stmt->fetch()) {
    header('Location: invoices.php');
    exit;
}

// Delete invoice items first
$stmt = $pdo->prepare("DELETE FROM invoice_items WHERE invoice_id = ?");
$stmt->execute([$invoice_id]);

// Then delete the invoice
$stmt = $pdo->prepare("DELETE FROM invoices WHERE id = ?");
$stmt->execute([$invoice_id]);

header('Location: invoices.php');
exit;
?>