<?php
require_once 'auth.php';
include 'config.php';

// Process form submission
if(isset($_POST['add_item'])) {
    $name = trim($_POST['name'] ?? '');
    $summary = trim($_POST['summary'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $price = filter_var($_POST['price'] ?? 0, FILTER_VALIDATE_FLOAT);

    // Validate required fields
    if(empty($name)) {
        $error = "Item name is required!";
    } elseif($price === false || $price < 0) {
        $error = "Invalid price format!";
    } else {
        try {
            $stmt = $pdo->prepare("INSERT INTO items (user_id, name, summary, description, price) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute([$_SESSION['user_id'], $name, $summary, $description, $price]);
            $_SESSION['success'] = "Item added successfully!";
            header("Location: items.php");
            exit();
        } catch(PDOException $e) {
            $error = "Error adding item. Please try again.";
        }
    }
}

// Set page title and include header AFTER form processing
$page_title = "Klay Invoice - Add Item";
include 'includes/header.php';
?>

<div class="container-fluid px-4">
    <div class="row mb-4">
        <div class="col">
            <h2 class="mt-4 mb-4"><i class="bi bi-box-plus"></i> Add Item</h2>
        </div>
    </div>

    <?php if (!empty($error)): ?>
        <div class="alert alert-danger">
            <?php echo htmlspecialchars($error); ?>
        </div>
    <?php endif; ?>

    <!-- Add Item Form -->
    <div class="card shadow-sm">
        <div class="card-body">
            <form method="post" class="needs-validation" novalidate>
                <div class="row g-3">
                    <div class="col-12">
                        <label for="name" class="form-label">Item Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" required>
                        <div class="invalid-feedback">Please enter item name.</div>
                    </div>
                    
                    <div class="col-12">
                        <label for="summary" class="form-label">Summary</label>
                        <input type="text" class="form-control" id="summary" name="summary">
                    </div>
                    
                    <div class="col-12">
                        <label for="price" class="form-label">Price (BDT) <span class="text-danger">*</span></label>
                        <input type="number" step="0.01" min="0" class="form-control" id="price" name="price" required>
                        <div class="invalid-feedback">Please enter a valid price.</div>
                    </div>
                    
                    <div class="col-12">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                </div>

                <div class="mt-4">
                    <button type="submit" name="add_item" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>Add Item
                    </button>
                    <a href="items.php" class="btn btn-outline-secondary ms-2">
                        <i class="bi bi-x-circle me-2"></i>Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?> 