<?php
// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

include 'config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Get all clients for the current user
$stmt = $pdo->prepare("SELECT * FROM clients WHERE user_id = ? ORDER BY name");
$stmt->execute([$_SESSION['user_id']]);
$clients = $stmt->fetchAll();

$page_title = "Klay Invoice - Clients";
include 'includes/header.php';

// Process form submissions BEFORE any output
$bin = filter_input(INPUT_POST, 'bin', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
$name = filter_input(INPUT_POST, 'name', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
$email = filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL);
$address = filter_input(INPUT_POST, 'address', FILTER_SANITIZE_FULL_SPECIAL_CHARS);

if(!filter_var($email, FILTER_VALIDATE_EMAIL) && !empty($email)) {
    $client_error = "Invalid email format!";
}

// Add Client
if(isset($_POST['add_client']) && (empty($email) || filter_var($email, FILTER_VALIDATE_EMAIL))){
    $stmt = $pdo->prepare("INSERT INTO clients (user_id, name, bin, email, address) VALUES (?, ?, ?, ?, ?)");
    $stmt->execute([$_SESSION['user_id'], $name, $bin, $email, $address]);
    $client_success = "Client added successfully!";
}

// Handle deletion
if(isset($_POST['delete_client'])){
    try {
        $stmt = $pdo->prepare("DELETE FROM clients WHERE id = ? AND user_id = ?");
        $stmt->execute([$_POST['delete_id'], $_SESSION['user_id']]);
        $delete_success = "Client deleted successfully!";
    } catch(PDOException $e) {
        $delete_error = "Cannot delete client with existing invoices!";
    }
}

// Display success/error messages
if(isset($client_error)) {
    echo "<div class='alert alert-danger text-center' role='alert'><i class='bi bi-exclamation-triangle-fill'></i> $client_error</div>";
}
if(isset($client_success)) {
    echo "<div class='alert alert-success text-center' role='alert'><i class='bi bi-check-circle-fill'></i> $client_success</div>";
}
if(isset($delete_error)) {
    echo "<div class='alert alert-danger text-center' role='alert'><i class='bi bi-exclamation-triangle-fill'></i> $delete_error</div>";
}
if(isset($delete_success)) {
    echo "<div class='alert alert-success text-center' role='alert'><i class='bi bi-check-circle-fill'></i> $delete_success</div>";
}

// Search functionality
$search = isset($_GET['search']) ? $_GET['search'] : '';
$searchCondition = '';
$params = [':user_id' => $_SESSION['user_id']];

if (!empty($search)) {
    $searchCondition = " AND (name LIKE :search_name OR bin LIKE :search_bin OR email LIKE :search_email OR address LIKE :search_address)";
    $params[':search_name'] = "%$search%";
    $params[':search_bin'] = "%$search%";
    $params[':search_email'] = "%$search%";
    $params[':search_address'] = "%$search%";
}

// Get Clients with search - only for current user
$stmt = $pdo->prepare("SELECT * FROM clients WHERE user_id = :user_id $searchCondition ORDER BY name ASC");
$stmt->execute($params);
$clients = $stmt->fetchAll();
?>

<div class="container-fluid px-4">
    <div class="row mb-4">
        <div class="col">
            <h2 class="mt-4 mb-4"><i class="bi bi-people"></i> Clients</h2>
        </div>
        <div class="col text-end">
            <a href="add_client.php" class="btn btn-primary mt-4">
                <i class="bi bi-plus-circle"></i> Add New Client
            </a>
        </div>
    </div>

    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success">
            <?php 
            echo $_SESSION['success'];
            unset($_SESSION['success']);
            ?>
        </div>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger">
            <?php 
            echo $_SESSION['error'];
            unset($_SESSION['error']);
            ?>
        </div>
    <?php endif; ?>

    <!-- Search Form -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="" class="row g-3 justify-content-center">
                <div class="col-md-6">
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-search"></i></span>
                        <input type="text" class="form-control" name="search" placeholder="Search by name, BIN, email, or address" 
                               value="<?php echo htmlspecialchars($_GET['search'] ?? ''); ?>">
                    </div>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary form-control">
                        <i class="bi bi-search me-1"></i> Search
                    </button>
                </div>
                <?php if (!empty($_GET['search'])): ?>
                    <div class="col-md-2">
                        <a href="clients.php" class="btn btn-outline-secondary form-control">
                            <i class="bi bi-x-circle me-1"></i> Clear
                        </a>
                    </div>
                <?php endif; ?>
            </form>
        </div>
    </div>

    <!-- Clients Table -->
    <div class="card shadow-sm">
        <div class="card-header bg-light">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0"><i class="bi bi-people"></i> Client List</h5>
                </div>
                <div class="col-auto">
                    <span class="badge bg-primary"><?= count($clients) ?> clients found</span>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover table-no-border table-striped mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th class="ps-3" width="5%">ID</th>
                            <th width="30%">Client Information</th>
                            <th width="40%">Contact Details</th>
                            <th class="text-center" width="25%">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if(count($clients) > 0): ?>
                            <?php foreach($clients as $client): ?>
                                <tr>
                                    <td class="ps-3"><?= $client['id'] ?></td>
                                    <td>
                                        <strong><?= htmlspecialchars($client['name']) ?></strong>
                                        <?php if(!empty($client['bin'])): ?>
                                            <br><span class="badge bg-secondary">BIN: <?= htmlspecialchars($client['bin']) ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if(!empty($client['address'])): ?>
                                            <i class="bi bi-geo-alt text-muted"></i> <?= htmlspecialchars($client['address']) ?><br>
                                        <?php endif; ?>
                                        <?php if(!empty($client['email'])): ?>
                                            <i class="bi bi-envelope text-muted"></i> <a href="mailto:<?= htmlspecialchars($client['email']) ?>"><?= htmlspecialchars($client['email']) ?></a>
                                        <?php endif; ?>
                                    </td>
                                    <td class="text-center">
                                        <div class="d-inline-flex gap-1">
                                            <a href="edit_client.php?id=<?= $client['id'] ?>" class="btn btn-sm btn-outline-warning border" title="Edit Client" data-bs-toggle="tooltip">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger border" data-bs-toggle="modal" data-bs-target="#deleteModal<?= $client['id'] ?>" title="Delete Client" data-bs-toggle="tooltip">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                        
                                        <!-- Delete Modal -->
                                        <div class="modal fade" id="deleteModal<?= $client['id'] ?>" tabindex="-1" aria-labelledby="deleteModalLabel<?= $client['id'] ?>" aria-hidden="true">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title" id="deleteModalLabel<?= $client['id'] ?>">Confirm Delete</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body text-start">
                                                        <p>Are you sure you want to delete client: <strong><?= htmlspecialchars($client['name']) ?></strong>?</p>
                                                        <p class="text-danger"><strong>Warning:</strong> This action cannot be undone. If this client has invoices, the deletion will fail.</p>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                        <form method="post" style="display:inline;">
                                                            <input type="hidden" name="delete_id" value="<?= $client['id'] ?>">
                                                            <button type="submit" name="delete_client" class="btn btn-danger">Delete Client</button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="4" class="text-center py-4">
                                    <i class="bi bi-info-circle text-info fs-4"></i><br>
                                    No clients found. Add a new client using the form above.
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>

<script>
// Initialize tooltips for action buttons
document.addEventListener('DOMContentLoaded', function() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
