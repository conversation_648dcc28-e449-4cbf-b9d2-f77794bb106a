<?php
// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

include 'config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Check if invoice ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: invoices.php');
    exit;
}

$invoice_id = $_GET['id'];

// Verify invoice belongs to user
$stmt = $pdo->prepare("SELECT * FROM invoices WHERE id = ? AND user_id = ?");
$stmt->execute([$invoice_id, $_SESSION['user_id']]);
$invoice = $stmt->fetch();

if (!$invoice) {
    header('Location: invoices.php');
    exit;
}

// Get email history for this invoice
$stmt = $pdo->prepare("SELECT * FROM email_logs WHERE invoice_id = ? AND user_id = ? ORDER BY sent_at DESC");
$stmt->execute([$invoice_id, $_SESSION['user_id']]);
$emails = $stmt->fetchAll();

$page_title = "View Emails";
include 'includes/header.php';

// View Saved Emails (Development Tool)
require_once 'email_fallback.php';

$viewEmail = null;

if (isset($_GET['view']) && !empty($_GET['view'])) {
    $filename = basename($_GET['view']); // Security: only basename
    $filepath = __DIR__ . '/emails/' . $filename;
    
    if (file_exists($filepath) && strpos($filename, 'email_') === 0) {
        $viewEmail = file_get_contents($filepath);
    }
}

// Delete email
if (isset($_POST['delete']) && !empty($_POST['delete'])) {
    $filename = basename($_POST['delete']);
    $filepath = __DIR__ . '/emails/' . $filename;
    
    if (file_exists($filepath) && strpos($filename, 'email_') === 0) {
        unlink($filepath);
        header("Location: view_emails.php");
        exit;
    }
}

// Clear all emails
if (isset($_POST['clear_all'])) {
    $files = glob(__DIR__ . '/emails/email_*.html');
    foreach ($files as $file) {
        unlink($file);
    }
    header("Location: view_emails.php");
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Viewer - Development Tool</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <style>
        body { background: #f8f9fa; }
        .email-preview { max-height: 600px; overflow-y: auto; border: 1px solid #dee2e6; }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="bi bi-envelope me-2"></i>Saved Emails</h5>
                        <?php if (!empty($emails)): ?>
                            <form method="post" class="d-inline">
                                <button type="submit" name="clear_all" class="btn btn-sm btn-outline-light" 
                                        onclick="return confirm('Clear all saved emails?')">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </form>
                        <?php endif; ?>
                    </div>
                    <div class="card-body p-0">
                        <?php if (empty($emails)): ?>
                            <div class="p-3 text-center text-muted">
                                <i class="bi bi-inbox display-4 d-block mb-2"></i>
                                No emails saved yet
                            </div>
                        <?php else: ?>
                            <div class="list-group list-group-flush">
                                <?php foreach ($emails as $email): ?>
                                    <div class="list-group-item d-flex justify-content-between align-items-start">
                                        <div class="flex-grow-1">
                                            <a href="?view=<?= urlencode($email['filename']) ?>" 
                                               class="text-decoration-none stretched-link">
                                                <div class="fw-bold"><?= htmlspecialchars($email['filename']) ?></div>
                                                <small class="text-muted"><?= $email['date'] ?></small>
                                            </a>
                                        </div>
                                        <form method="post" class="ms-2" style="position: relative; z-index: 10;">
                                            <button type="submit" name="delete" value="<?= htmlspecialchars($email['filename']) ?>" 
                                                    class="btn btn-sm btn-outline-danger"
                                                    onclick="return confirm('Delete this email?')">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0"><i class="bi bi-info-circle me-2"></i>Development Mode</h6>
                    </div>
                    <div class="card-body">
                        <p class="small mb-2">
                            When SMTP is not working, emails are saved as HTML files for testing.
                        </p>
                        <div class="d-grid gap-2">
                            <a href="debug_email.php" class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-bug me-1"></i>Debug SMTP
                            </a>
                            <a href="test_email.php" class="btn btn-sm btn-outline-secondary">
                                <i class="bi bi-send me-1"></i>Test Email
                            </a>
                            <a href="register.php" class="btn btn-sm btn-outline-success">
                                <i class="bi bi-person-plus me-1"></i>Test Registration
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0"><i class="bi bi-eye me-2"></i>Email Preview</h5>
                    </div>
                    <div class="card-body p-0">
                        <?php if ($viewEmail): ?>
                            <div class="email-preview">
                                <?= $viewEmail ?>
                            </div>
                        <?php else: ?>
                            <div class="p-5 text-center text-muted">
                                <i class="bi bi-envelope-open display-4 d-block mb-3"></i>
                                <h5>Select an email to preview</h5>
                                <p>Click on an email from the list to view its content</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
