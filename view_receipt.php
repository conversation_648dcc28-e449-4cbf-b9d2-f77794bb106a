<?php
// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

include 'config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Check if invoice ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: invoices.php');
    exit;
}

$invoice_id = $_GET['id'];

// Get invoice with user access check
$stmt = $pdo->prepare("SELECT * FROM invoices 
                      JOIN clients ON invoices.client_id = clients.id
                      WHERE invoices.id = ? AND invoices.user_id = ?");
$stmt->execute([$invoice_id, $_SESSION['user_id']]);
$invoice = $stmt->fetch();

if (!$invoice) {
    header('Location: invoices.php');
    exit;
}

$items = $pdo->query("SELECT * FROM invoice_items 
                     JOIN items ON invoice_items.item_id = items.id
                     WHERE invoice_id = $invoice_id")->fetchAll();

// Calculate Totals
$subtotal = 0;
foreach ($items as $item){
    $subtotal += $item['unit_price'] * $item['quantity'];
}

$discount_rate = $subtotal * ($invoice['discount_rate'] / 100);
$discount_fixed = $invoice['discount_fixed'];
$total = $subtotal - ($discount_rate + $discount_fixed);
$tax = $total * ($invoice['vat_rate'] / 100);
$total_grand = $total + $tax;

$invoice_for = $invoice['invoice_for'];
$service_type = $invoice['service_type'];
$special_notes = $item['special_notes'];

// Add null checks for dates
$invoice_date = !empty($invoice['invoice_date']) ? date("d/m/Y", strtotime($invoice['invoice_date'])) : 'Not Set';
$invoice_id_date = !empty($invoice['invoice_date']) ? date("Y", strtotime($invoice['invoice_date'])) : date("Y");
//$due_date = !empty($invoice['due_date']) ? date("d M Y", strtotime($invoice['due_date'])) : 'Not Set';

function numberToWords($number) {
    $ones = array('Zero', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine');
    $teens = array('Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen', 'Seventeen', 'Eighteen', 'Nineteen');
    $tens = array('Ten', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety');
    $hundreds = array('Hundred', 'Thousand', 'Million', 'Billion');

    $number = round($number, 2); // Round the number to 2 decimal places

    $dollars = intval($number);
    $cents = round(($number - $dollars) * 100);

    $words = '';

    if ($dollars > 0) {
        $words .= numberToWordsHelper($dollars) . ' Taka ';
    }

    if ($cents > 0) {
        $words .= 'and ' . numberToWordsHelper($cents) . ' Poisha';
    }

    return trim($words);
}

function numberToWordsHelper($number) {
    $ones = array('Zero', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine');
    $teens = array('Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen', 'Seventeen', 'Eighteen', 'Nineteen');
    $tens = array('Ten', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety');
    $hundreds = array('Hundred', 'Thousand', 'Million', 'Billion');

    if ($number < 10) {
        return $ones[$number];
    } elseif ($number < 20) {
        return $teens[$number - 11];
    } elseif ($number < 100) {
        return $tens[intval($number / 10) - 1] . ' ' . ($number % 10 == 0 ? '' : numberToWordsHelper($number % 10));
    } elseif ($number < 1000) {
        return $ones[intval($number / 100)] . ' ' . $hundreds[0] . ' ' . ($number % 100 == 0 ? '' : numberToWordsHelper($number % 100));
    } elseif ($number < 1000000) {
        return numberToWordsHelper(intval($number / 1000)) . ' ' . $hundreds[1] . ' ' . ($number % 1000 == 0 ? '' : numberToWordsHelper($number % 1000));
    } elseif ($number < 1000000000) {
        return numberToWordsHelper(intval($number / 1000000)) . ' ' . $hundreds[2] . ' ' . ($number % 1000000 == 0 ? '' : numberToWordsHelper($number % 1000000));
    } elseif ($number < 1000000000000) {
        return numberToWordsHelper(intval($number / 1000000000)) . ' ' . $hundreds[3] . ' ' . ($number % 1000000000 == 0 ? '' : numberToWordsHelper($number % 1000000000));
    }
}
?>

    <!DOCTYPE html>
    <html lang="en">

    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title><?php echo "Receipt: " . $_GET['id']; ?></title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;700&display=swap" rel="stylesheet">
        <style>
            body {
                font-family: 'Montserrat', sans-serif;
                font-size: 12px;
                background-color: #f0f0f0;
                padding: 20px;
            }
            .print-wrap {
                width: 210mm;
                min-height: 297mm;
                padding: 15mm;
                margin: 0 auto;
                background: white;
                box-shadow: 0 0 10px rgba(0,0,0,0.1);
                position: relative;
            }
            .action-buttons {
                position: fixed;
                top: 20px;
                left: 0;
                right: 0;
                z-index: 1000;
                padding: 0 20px;
                display: flex;
                justify-content: space-between;
            }
            .action-buttons .btn {
                padding: 8px 20px;
                font-weight: 500;
            }

            .brand-logo {
                position: absolute;
                top: 16mm;
                right: 15mm;
            }

            footer {
                font-size: 10px;
                position: absolute !important;
                bottom: 10mm !important;
            }

            @media print {
                body {
                    background: none;
                    padding: 0;
                }
                .print-wrap {
                    width: 100%;
                    min-height: auto;
                    padding: 5mm;
                    margin: 0;
                    box-shadow: none;
                }

                .brand-logo {
                    top: 6mm;
                    right: 5mm;
                }
                .action-buttons {
                    display: none;
                }
                footer {
                    bottom: 0 !important;
                }
            }
            .cl-info label {
                display: block;
                line-height: 1.6em;
                border-bottom: 1px solid #ddd;
                padding-top: 3px;
                padding-bottom: 3px;
            }
            .kl-amount * {
                border-color: #999;
                font-size: 15px;
            }
            .kl-amount td,
            .kl-amount th {
                padding: 5px 15px;
            }
            .kl-amount th {
                background-color: #F5F5F5;
                font-weight: 700;
            }
            .kl-payment-by .form-check {
                display: inline-block;
                margin-right: 20px;
            }
        </style>
    </head>

    <body>
    <div class="action-buttons">
        <a href="javascript:history.back()" class="btn btn-secondary">
            <i class="bi bi-arrow-left"></i> Back
        </a>
        <button onclick="window.print()" class="btn btn-primary">
            <i class="bi bi-printer"></i> Print
        </button>
    </div>

    <div class="container-fluid print-wrap mt-2 mb-5">
        <div class="kl-head">
            <h3 class="fw-bold mb-0">
                PAYMENT RECEIPT
            </h3>
            <label style="font-size: 12px;"><strong>RECEIPT NO:</strong> <?= $invoice_id_date ?><?= $invoice_id ?></label><br>
            <label style="font-size: 12px;"><strong>DATE:</strong> <?= $invoice_date ?></label>
        </div>
        <img src="assets/img/logo.svg" class="brand-logo img-fluid" width="300">
        
        <div class="mt-3 cl-info"> 
            <label><strong>RECEIVED FROM:</strong> <?= $invoice['name'] ?></label>            <label><strong>ADDRESS:</strong> <?= $invoice['address'] ?></label>
            <label><strong>PURPOSE OF PAYMENT:</strong> <?= $invoice['invoice_for'] ?> - <?= $invoice['service_type'] ?></label>
            <label><strong>AMOUNT (In Words):</strong> <?php echo numberToWords($total_grand); ?></label>
        </div>
        
        <div class="row mt-4">
            <div class="col-6">
                <table class="table table-bordered kl-amount">
                    <tr>
                        <th>TOTAL</th>
                        <td><?= number_format($total_grand, 2) ?></td>
                    </tr>
                    <tr>
                        <th>PAID</th>
                        <td><?= number_format($total_grand, 2) ?></td>
                    </tr>
                    <tr>
                        <th>DUE</th>
                        <td>0.00</td>
                    </tr>
                </table>
            </div>
            <div class="col-6">
                <div class="kl-payment-by">
                    <p class="fw-bold mb-2">PAYMENT MADE BY</p>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="cash">
                        <label class="form-check-label" for="cash">CASH</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="cheque">
                        <label class="form-check-label" for="cheque">CHEQUE</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="eft">
                        <label class="form-check-label" for="eft">EFT</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="others">
                        <label class="form-check-label" for="others">OTHERS</label>
                    </div>
                    <div class="mt-2">
                        <label><strong>NOTES</strong> (If Any):</label>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-4 row">
            <div class="col-7" style="font-size: 10px;">
                <p class="mt-1"><strong>Office Address:</strong> House# 460/A, Road# 6, Avenue# 7, Mirpur DOHS, Dhaka<br>
                <strong>Phone:</strong> 01552454543 <strong>Email:</strong> <EMAIL> <strong>Web:</strong> klay.tech</p>
            </div>
            <div class="col-5 text-end">
                <label class="fw-bold border-top mt-0 pt-2">AUTHORIZED SIGNATURE</label>
                <div>(Nasim Ahmed)</div>
            </div>
        </div>
    </div>

        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
        <script>
            $(document).ready(function() {
                // Move footer inside print-wrap for screen display
                $('.print-wrap').append($('footer'));
                
                // Handle print event
                window.onbeforeprint = function() {
                    // Move footer back to body for printing
                    $('body').append($('footer'));
                };
                
                window.onafterprint = function() {
                    // Move footer back inside print-wrap after printing
                    $('.print-wrap').append($('footer'));
                };
            });
        </script>
    </body>

    </html>