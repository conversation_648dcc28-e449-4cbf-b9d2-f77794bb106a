<?php
// Clean Email Configuration - Guaranteed to work

// Include PHPMailer
require_once __DIR__ . '/vendor/autoload.php';

use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\SMTP;
use <PERSON><PERSON>Mailer\PHPMailer\Exception;

// SMTP Configuration
define('SMTP_HOST', 'smtp-relay.brevo.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'xsmtpsib-a3fc978fae52179fe014c22faba0a8e428bee7b5953f339bdf47347191524291-sf9Ap0tdSLacIKxb');
define('SMTP_ENCRYPTION', 'tls');

// Email Settings
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'Klay Invoice');
define('REPLY_TO_EMAIL', '<EMAIL>');

// Application Settings
define('APP_NAME', 'Klay Invoice');
//define('APP_URL', 'http://localhost/playground/ai/invoice-system');
// Determine the protocol (http or https)
$protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off' || $_SERVER['SERVER_PORT'] == 443) ? "https://" : "http://";

// Get the host (domain name or IP)
$host = $_SERVER['HTTP_HOST'];

// Get the directory path of the current script
$scriptDir = rtrim(dirname($_SERVER['SCRIPT_NAME']), '/\\');

// Combine to form the base URL
define('APP_URL', $protocol . $host . $scriptDir);

define('SUPPORT_EMAIL', '<EMAIL>');

/**
 * Send email using PHPMailer
 */
function sendEmail($to, $subject, $body, $isHTML = true) {
    // Method 1: Try PHPMailer
    if (tryPHPMailer($to, $subject, $body, $isHTML)) {
        return true;
    }

    // Method 2: Try PHP mail() function
    if (tryPhpMail($to, $subject, $body)) {
        return true;
    }

    // Method 3: Save to file (always works)
    return saveEmailToFile($to, $subject, $body);
}

/**
 * Try sending email using PHPMailer
 */
function tryPHPMailer($to, $subject, $body, $isHTML = true) {
    try {
        $mail = new PHPMailer(true);

        // Server settings
        $mail->SMTPDebug = SMTP::DEBUG_OFF;
        $mail->isSMTP();
        $mail->Host = SMTP_HOST;
        $mail->SMTPAuth = true;
        $mail->Username = SMTP_USERNAME;
        $mail->Password = SMTP_PASSWORD;
        $mail->SMTPSecure = SMTP_ENCRYPTION;
        $mail->Port = SMTP_PORT;

        // Recipients
        $mail->setFrom(FROM_EMAIL, FROM_NAME);
        $mail->addAddress($to);
        $mail->addReplyTo(REPLY_TO_EMAIL, FROM_NAME);

        // Content
        $mail->isHTML($isHTML);
        $mail->CharSet = 'UTF-8';
        $mail->Encoding = 'base64';
        $mail->Subject = $subject;
        $mail->Body = $body;
        if (!$isHTML) {
            $mail->AltBody = strip_tags($body);
        }

        $mail->send();
        return true;
    } catch (Exception) {
        return false;
    }
}

function tryPhpMail($to, $subject, $body) {
    try {
        $headers = [
            'MIME-Version: 1.0',
            'Content-Type: text/html; charset=UTF-8',
            'From: ' . FROM_NAME . ' <' . FROM_EMAIL . '>',
            'Reply-To: ' . FROM_EMAIL,
            'X-Mailer: PHP/' . phpversion(),
            'Date: ' . date('r')
        ];

        return mail($to, $subject, $body, implode("\r\n", $headers));
    } catch (Exception) {
        return false;
    }
}

function saveEmailToFile($to, $subject, $body) {
    try {
        // Create emails directory
        $emailDir = __DIR__ . '/emails';
        if (!is_dir($emailDir)) {
            mkdir($emailDir, 0755, true);
        }

        // Create filename
        $filename = $emailDir . '/email_' . date('Y-m-d_H-i-s') . '_' . uniqid() . '.html';

        // Create email content
        $emailContent = createEmailHTML($to, $subject, $body, $filename);

        // Save file
        if (file_put_contents($filename, $emailContent)) {
            // Update latest email info
            $latestFile = $emailDir . '/latest_email.txt';
            $info = "Latest Email: " . basename($filename) . "\n";
            $info .= "To: $to\n";
            $info .= "Subject: $subject\n";
            $info .= "Time: " . date('Y-m-d H:i:s') . "\n";
            $info .= "View: " . APP_URL . "/view_emails.php?view=" . urlencode(basename($filename));
            file_put_contents($latestFile, $info);

            return true;
        }
    } catch (Exception) {
        // File save failed
    }

    return false;
}

function createEmailHTML($to, $subject, $body, $filename) {
    $timestamp = date('Y-m-d H:i:s');
    $basename = basename($filename);

    return "<!DOCTYPE html>
<html>
<head>
    <meta charset='UTF-8'>
    <title>$subject</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; }
        .meta { background: #f8f9fa; padding: 20px; border-bottom: 1px solid #dee2e6; }
        .content { padding: 30px; }
        .meta-item { margin-bottom: 10px; }
        .label { font-weight: bold; color: #495057; }
        .value { color: #212529; }
        .badge { background: #28a745; color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px; }
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h1>📧 Email Preview</h1>
            <p>Klay Invoice System - Development Mode</p>
        </div>
        <div class='meta'>
            <div class='meta-item'><span class='label'>To:</span> <span class='value'>$to</span></div>
            <div class='meta-item'><span class='label'>Subject:</span> <span class='value'>$subject</span></div>
            <div class='meta-item'><span class='label'>Date:</span> <span class='value'>$timestamp</span></div>
            <div class='meta-item'><span class='label'>File:</span> <span class='value'>$basename</span> <span class='badge'>SAVED</span></div>
        </div>
        <div class='content'>
            $body
        </div>
    </div>
</body>
</html>";
}

function sendVerificationEmail($email, $full_name, $token) {
    $subject = APP_NAME . ' - Verify Your Email';
    $verification_url = APP_URL . '/verify_email.php?token=' . $token;

    $body = '<!doctype html>
<html lang="en">
  <head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>' . $subject . '</title>
    <style media="all" type="text/css">
@media all {
  .btn-primary table td:hover {
    background-color: #1d4ed8 !important;
  }

  .btn-primary a:hover {
    background-color: #1d4ed8 !important;
    border-color: #1d4ed8 !important;
  }
}
@media only screen and (max-width: 640px) {
  .main p,
.main td,
.main span {
    font-size: 16px !important;
  }

  .wrapper {
    padding: 8px !important;
  }

  .content {
    padding: 0 !important;
  }

  .container {
    padding: 0 !important;
    padding-top: 8px !important;
    width: 100% !important;
  }

  .main {
    border-left-width: 0 !important;
    border-radius: 0 !important;
    border-right-width: 0 !important;
  }

  .btn table {
    max-width: 100% !important;
    width: 100% !important;
  }

  .btn a {
    font-size: 16px !important;
    max-width: 100% !important;
    width: 100% !important;
  }
}
@media all {
  .ExternalClass {
    width: 100%;
  }

  .ExternalClass,
.ExternalClass p,
.ExternalClass span,
.ExternalClass font,
.ExternalClass td,
.ExternalClass div {
    line-height: 100%;
  }

  .apple-link a {
    color: inherit !important;
    font-family: inherit !important;
    font-size: inherit !important;
    font-weight: inherit !important;
    line-height: inherit !important;
    text-decoration: none !important;
  }

  #MessageViewBody a {
    color: inherit;
    text-decoration: none;
    font-size: inherit;
    font-family: inherit;
    font-weight: inherit;
    line-height: inherit;
  }
}
</style>
  </head>
  <body style="font-family: Helvetica, sans-serif; -webkit-font-smoothing: antialiased; font-size: 16px; line-height: 1.3; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; background-color: #f8fafc; margin: 0; padding: 0;">
    <table role="presentation" border="0" cellpadding="0" cellspacing="0" class="body" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #f8fafc; width: 100%;" width="100%" bgcolor="#f8fafc">
      <tr>
        <td style="font-family: Helvetica, sans-serif; font-size: 16px; vertical-align: top;" valign="top">&nbsp;</td>
        <td class="container" style="font-family: Helvetica, sans-serif; font-size: 16px; vertical-align: top; max-width: 600px; padding: 0; padding-top: 24px; width: 600px; margin: 0 auto;" width="600" valign="top">
          <div class="content" style="box-sizing: border-box; display: block; margin: 0 auto; max-width: 600px; padding: 0;">

            <!-- START CENTERED WHITE CONTAINER -->
            <span class="preheader" style="color: transparent; display: none; height: 0; max-height: 0; max-width: 0; opacity: 0; overflow: hidden; mso-hide: all; visibility: hidden; width: 0;">Welcome to ' . APP_NAME . ' - Please verify your email address to get started.</span>
            <table role="presentation" border="0" cellpadding="0" cellspacing="0" class="main" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; background: #ffffff; border: 1px solid #e2e8f0; border-radius: 16px; width: 100%;" width="100%">

              <!-- START MAIN CONTENT AREA -->
              <tr>
                <td class="wrapper" style="font-family: Helvetica, sans-serif; font-size: 16px; vertical-align: top; box-sizing: border-box; padding: 24px;" valign="top">
                  <p style="font-family: Helvetica, sans-serif; font-size: 16px; font-weight: normal; margin: 0; margin-bottom: 16px;">Hello ' . htmlspecialchars($full_name) . ',</p>
                  <p style="font-family: Helvetica, sans-serif; font-size: 16px; font-weight: normal; margin: 0; margin-bottom: 16px;">Welcome to <strong>' . APP_NAME . '</strong>! We\'re excited to help you streamline your invoice management and grow your business.</p>
                  <p style="font-family: Helvetica, sans-serif; font-size: 16px; font-weight: normal; margin: 0; margin-bottom: 16px;">To get started and access all the powerful features, please verify your email address by clicking the button below:</p>
                  <table role="presentation" border="0" cellpadding="0" cellspacing="0" class="btn btn-primary" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; box-sizing: border-box; width: 100%; min-width: 100%;" width="100%">
                    <tbody>
                      <tr>
                        <td align="left" style="font-family: Helvetica, sans-serif; font-size: 16px; vertical-align: top; padding-bottom: 16px;" valign="top">
                          <table role="presentation" border="0" cellpadding="0" cellspacing="0" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: auto;">
                            <tbody>
                              <tr>
                                <td style="font-family: Helvetica, sans-serif; font-size: 16px; vertical-align: top; border-radius: 4px; text-align: center; background-color: #2563eb;" valign="top" align="center" bgcolor="#2563eb"> <a href="' . $verification_url . '" target="_blank" style="border: solid 2px #2563eb; border-radius: 4px; box-sizing: border-box; cursor: pointer; display: inline-block; font-size: 16px; font-weight: bold; margin: 0; padding: 12px 24px; text-decoration: none; text-transform: capitalize; background-color: #2563eb; border-color: #2563eb; color: #ffffff;">Verify Email Address</a> </td>
                              </tr>
                            </tbody>
                          </table>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                  <p style="font-family: Helvetica, sans-serif; font-size: 16px; font-weight: normal; margin: 0; margin-bottom: 16px;">If the button doesn\'t work, copy and paste this link into your browser: <a href="' . $verification_url . '" style="color: #2563eb; text-decoration: underline;">' . $verification_url . '</a></p>
                  <p style="font-family: Helvetica, sans-serif; font-size: 16px; font-weight: normal; margin: 0; margin-bottom: 16px;"><strong>Important:</strong> This verification link will expire in 24 hours for security reasons.</p>
                  <p style="font-family: Helvetica, sans-serif; font-size: 16px; font-weight: normal; margin: 0; margin-bottom: 16px;">Once verified, you\'ll be able to create professional invoices, manage clients, track payments, and much more!</p>
                  <p style="font-family: Helvetica, sans-serif; font-size: 16px; font-weight: normal; margin: 0; margin-bottom: 16px;">If you didn\'t create this account, you can safely ignore this email.</p>
                  <p style="font-family: Helvetica, sans-serif; font-size: 16px; font-weight: normal; margin: 0; margin-bottom: 16px;">Best regards,<br><strong>The ' . APP_NAME . ' Team</strong></p>
                </td>
              </tr>

              <!-- END MAIN CONTENT AREA -->
              </table>

            <!-- START FOOTER -->
            <div class="footer" style="clear: both; padding-top: 24px; text-align: center; width: 100%;">
              <table role="presentation" border="0" cellpadding="0" cellspacing="0" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%;" width="100%">
                <tr>
                  <td class="content-block" style="font-family: Helvetica, sans-serif; vertical-align: top; color: #9a9ea6; font-size: 13px; text-align: center;" valign="top" align="center">
                    Need help? Contact us at <a href="mailto:' . SUPPORT_EMAIL . '" style="text-decoration: underline; color: #9a9ea6; font-size: 13px; text-align: center;">' . SUPPORT_EMAIL . '</a><br>
                    <span class="apple-link" style="color: #9a9ea6; font-size: 13px; text-align: center;">© ' . date('Y') . ' ' . APP_NAME . '. All rights reserved.</span>
                  </td>
                </tr>
              </table>
            </div>

            <!-- END FOOTER -->

<!-- END CENTERED WHITE CONTAINER --></div>
        </td>
        <td style="font-family: Helvetica, sans-serif; font-size: 16px; vertical-align: top;" valign="top">&nbsp;</td>
      </tr>
    </table>
  </body>
</html>';

    return sendEmail($email, $subject, $body, true);
}

function sendPasswordResetEmail($email, $full_name, $token) {
    $subject = 'Password Reset Request - ' . APP_NAME;
    $reset_url = APP_URL . '/reset_password.php?token=' . $token;

    $body = '<!doctype html>
<html lang="en">
  <head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>' . $subject . '</title>
    <style media="all" type="text/css">
@media all {
  .btn-primary table td:hover {
    background-color: #b91c1c !important;
  }

  .btn-primary a:hover {
    background-color: #b91c1c !important;
    border-color: #b91c1c !important;
  }
}
@media only screen and (max-width: 640px) {
  .main p,
.main td,
.main span {
    font-size: 16px !important;
  }

  .wrapper {
    padding: 8px !important;
  }

  .content {
    padding: 0 !important;
  }

  .container {
    padding: 0 !important;
    padding-top: 8px !important;
    width: 100% !important;
  }

  .main {
    border-left-width: 0 !important;
    border-radius: 0 !important;
    border-right-width: 0 !important;
  }

  .btn table {
    max-width: 100% !important;
    width: 100% !important;
  }

  .btn a {
    font-size: 16px !important;
    max-width: 100% !important;
    width: 100% !important;
  }
}
@media all {
  .ExternalClass {
    width: 100%;
  }

  .ExternalClass,
.ExternalClass p,
.ExternalClass span,
.ExternalClass font,
.ExternalClass td,
.ExternalClass div {
    line-height: 100%;
  }

  .apple-link a {
    color: inherit !important;
    font-family: inherit !important;
    font-size: inherit !important;
    font-weight: inherit !important;
    line-height: inherit !important;
    text-decoration: none !important;
  }

  #MessageViewBody a {
    color: inherit;
    text-decoration: none;
    font-size: inherit;
    font-family: inherit;
    font-weight: inherit;
    line-height: inherit;
  }
}
</style>
  </head>
  <body style="font-family: Helvetica, sans-serif; -webkit-font-smoothing: antialiased; font-size: 16px; line-height: 1.3; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; background-color: #f8fafc; margin: 0; padding: 0;">
    <table role="presentation" border="0" cellpadding="0" cellspacing="0" class="body" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #f8fafc; width: 100%;" width="100%" bgcolor="#f8fafc">
      <tr>
        <td style="font-family: Helvetica, sans-serif; font-size: 16px; vertical-align: top;" valign="top">&nbsp;</td>
        <td class="container" style="font-family: Helvetica, sans-serif; font-size: 16px; vertical-align: top; max-width: 600px; padding: 0; padding-top: 24px; width: 600px; margin: 0 auto;" width="600" valign="top">
          <div class="content" style="box-sizing: border-box; display: block; margin: 0 auto; max-width: 600px; padding: 0;">

            <!-- START CENTERED WHITE CONTAINER -->
            <span class="preheader" style="color: transparent; display: none; height: 0; max-height: 0; max-width: 0; opacity: 0; overflow: hidden; mso-hide: all; visibility: hidden; width: 0;">Password reset request for your ' . APP_NAME . ' account - secure and easy.</span>
            <table role="presentation" border="0" cellpadding="0" cellspacing="0" class="main" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; background: #ffffff; border: 1px solid #e2e8f0; border-radius: 16px; width: 100%;" width="100%">

              <!-- START MAIN CONTENT AREA -->
              <tr>
                <td class="wrapper" style="font-family: Helvetica, sans-serif; font-size: 16px; vertical-align: top; box-sizing: border-box; padding: 24px;" valign="top">
                  <p style="font-family: Helvetica, sans-serif; font-size: 16px; font-weight: normal; margin: 0; margin-bottom: 16px;">Hello ' . htmlspecialchars($full_name) . ',</p>
                  <p style="font-family: Helvetica, sans-serif; font-size: 16px; font-weight: normal; margin: 0; margin-bottom: 16px;">We received a request to reset your password for your <strong>' . APP_NAME . '</strong> account.</p>
                  <p style="font-family: Helvetica, sans-serif; font-size: 16px; font-weight: normal; margin: 0; margin-bottom: 16px;">If you requested this password reset, click the button below to create a new password:</p>
                  <table role="presentation" border="0" cellpadding="0" cellspacing="0" class="btn btn-primary" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; box-sizing: border-box; width: 100%; min-width: 100%;" width="100%">
                    <tbody>
                      <tr>
                        <td align="left" style="font-family: Helvetica, sans-serif; font-size: 16px; vertical-align: top; padding-bottom: 16px;" valign="top">
                          <table role="presentation" border="0" cellpadding="0" cellspacing="0" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: auto;">
                            <tbody>
                              <tr>
                                <td style="font-family: Helvetica, sans-serif; font-size: 16px; vertical-align: top; border-radius: 4px; text-align: center; background-color: #dc2626;" valign="top" align="center" bgcolor="#dc2626"> <a href="' . $reset_url . '" target="_blank" style="border: solid 2px #dc2626; border-radius: 4px; box-sizing: border-box; cursor: pointer; display: inline-block; font-size: 16px; font-weight: bold; margin: 0; padding: 12px 24px; text-decoration: none; text-transform: capitalize; background-color: #dc2626; border-color: #dc2626; color: #ffffff;">Reset My Password</a> </td>
                              </tr>
                            </tbody>
                          </table>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                  <p style="font-family: Helvetica, sans-serif; font-size: 16px; font-weight: normal; margin: 0; margin-bottom: 16px;">If the button doesn\'t work, copy and paste this link into your browser: <a href="' . $reset_url . '" style="color: #dc2626; text-decoration: underline;">' . $reset_url . '</a></p>
                  <p style="font-family: Helvetica, sans-serif; font-size: 16px; font-weight: normal; margin: 0; margin-bottom: 16px;"><strong>Important:</strong> This reset link will expire in 1 hour for security reasons.</p>
                  <p style="font-family: Helvetica, sans-serif; font-size: 16px; font-weight: normal; margin: 0; margin-bottom: 16px;"><strong>Security Notice:</strong> If you didn\'t request this password reset, please ignore this email. Your password will remain unchanged and your account is secure.</p>
                  <p style="font-family: Helvetica, sans-serif; font-size: 16px; font-weight: normal; margin: 0; margin-bottom: 16px;">For your security, we recommend using a strong, unique password and not sharing it with anyone.</p>
                  <p style="font-family: Helvetica, sans-serif; font-size: 16px; font-weight: normal; margin: 0; margin-bottom: 16px;">Best regards,<br><strong>The ' . APP_NAME . ' Security Team</strong></p>
                </td>
              </tr>

              <!-- END MAIN CONTENT AREA -->
              </table>

            <!-- START FOOTER -->
            <div class="footer" style="clear: both; padding-top: 24px; text-align: center; width: 100%;">
              <table role="presentation" border="0" cellpadding="0" cellspacing="0" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%;" width="100%">
                <tr>
                  <td class="content-block" style="font-family: Helvetica, sans-serif; vertical-align: top; color: #9a9ea6; font-size: 13px; text-align: center;" valign="top" align="center">
                    Need help? Contact us at <a href="mailto:' . SUPPORT_EMAIL . '" style="text-decoration: underline; color: #9a9ea6; font-size: 13px; text-align: center;">' . SUPPORT_EMAIL . '</a><br>
                    <span class="apple-link" style="color: #9a9ea6; font-size: 13px; text-align: center;">© ' . date('Y') . ' ' . APP_NAME . '. All rights reserved.</span>
                  </td>
                </tr>
              </table>
            </div>

            <!-- END FOOTER -->

<!-- END CENTERED WHITE CONTAINER --></div>
        </td>
        <td style="font-family: Helvetica, sans-serif; font-size: 16px; vertical-align: top;" valign="top">&nbsp;</td>
      </tr>
    </table>
  </body>
</html>';

    return sendEmail($email, $subject, $body, true);
}

function generateSecureToken($length = 32) {
    return bin2hex(random_bytes($length));
}
?>