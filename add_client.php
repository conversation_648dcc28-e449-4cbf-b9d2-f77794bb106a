<?php
require_once 'auth.php';
include 'config.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

// Process form submission
if(isset($_POST['add_client'])) {
    $name = trim($_POST['name'] ?? '');
    $bin = trim($_POST['bin'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $address = trim($_POST['address'] ?? '');

    // Validate required fields
    if(empty($name)) {
        $error = "Client name is required!";
    } elseif(empty($address)) {
        $error = "Address is required!";
    } else {
        // Validate email if provided
        if(!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $error = "Invalid email format!";
        } else {
            try {
                $stmt = $pdo->prepare("INSERT INTO clients (user_id, name, bin, email, address) VALUES (?, ?, ?, ?, ?)");
                $stmt->execute([$_SESSION['user_id'], $name, $bin, $email, $address]);
                $_SESSION['success'] = "Client added successfully!";
                header("Location: clients.php");
                exit();
            } catch(PDOException $e) {
                $error = "Error adding client. Please try again.";
            }
        }
    }
}

// Set page title and include header AFTER form processing
$page_title = "Klay Invoice - Add Client";
include 'includes/header.php';
?>

<div class="container-fluid px-4">
    <div class="row mb-4">
        <div class="col">
            <h2 class="mt-4 mb-4"><i class="bi bi-person-plus"></i> Add New Client</h2>
        </div>
    </div>

    <?php if (!empty($error)): ?>
        <div class="alert alert-danger">
            <?php echo htmlspecialchars($error); ?>
        </div>
    <?php endif; ?>

    <!-- Add Client Form -->
    <div class="card shadow-sm">
        <div class="card-body">
            <form method="post" class="needs-validation" novalidate>
                <div class="row g-3">
                    <div class="col-12">
                        <label for="name" class="form-label">Client Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" required 
                               value="<?php echo htmlspecialchars($_POST['name'] ?? ''); ?>">
                        <div class="invalid-feedback">Please enter client name.</div>
                    </div>
                    
                    <div class="col-12">
                        <label for="bin" class="form-label">BIN</label>
                        <input type="text" class="form-control" id="bin" name="bin" 
                               value="<?php echo htmlspecialchars($_POST['bin'] ?? ''); ?>">
                    </div>
                    
                    <div class="col-12">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email" 
                               value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>">
                        <div class="invalid-feedback">Please enter a valid email address.</div>
                    </div>
                    
                    <div class="col-12">
                        <label for="address" class="form-label">Address <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="address" name="address" rows="3" required><?php echo htmlspecialchars($_POST['address'] ?? ''); ?></textarea>
                        <div class="invalid-feedback">Please enter address.</div>
                    </div>
                </div>

                <div class="mt-4">
                    <button type="submit" name="add_client" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>Add Client
                    </button>
                    <a href="clients.php" class="btn btn-outline-secondary ms-2">
                        <i class="bi bi-x-circle me-2"></i>Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?> 