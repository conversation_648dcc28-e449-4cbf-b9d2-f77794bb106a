<?php
// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

include 'config.php';
include 'auth.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Check if invoice ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: invoices.php');
    exit;
}

$invoice_id = $_GET['id'];

// Get invoice with user access check
$stmt = $pdo->prepare("SELECT * FROM invoices WHERE id = ? AND user_id = ?");
$stmt->execute([$invoice_id, $_SESSION['user_id']]);
$invoice = $stmt->fetch();

if (!$invoice) {
    header('Location: invoices.php');
    exit;
}

$page_title = "Tax Exempt Certificate - Invoice ID: KL-INV-" . $invoice_id;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $page_title ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Montserrat', sans-serif;
            font-size: 12px;
            background-color: #f0f0f0;
            padding: 20px;
        }
        .print-wrap {
            width: 210mm;
            min-height: 297mm;
            padding: 15mm;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            position: relative;
        }
        .action-buttons {
            position: fixed;
            top: 20px;
            left: 0;
            right: 0;
            z-index: 1000;
            padding: 0 20px;
            display: flex;
            justify-content: space-between;
        }
        .action-buttons .btn {
            padding: 8px 20px;
            font-weight: 500;
        }
        .pdf-container {
            width: 100%;
            height: calc(297mm - 30mm);
            border: none;
            background: white;
        }
        @media print {
            body {
                background: none;
                padding: 0;
            }
            .print-wrap {
                width: 100%;
                min-height: auto;
                padding: 5mm;
                margin: 0;
                box-shadow: none;
            }
            .action-buttons {
                display: none;
            }
            .pdf-container {
                height: calc(100vh - 10mm);
            }
        }
    </style>
</head>
<body>
    <div class="action-buttons">
        <a href="javascript:history.back()" class="btn btn-secondary">
            <i class="bi bi-arrow-left"></i> Back
        </a>
        <button onclick="printPDF()" class="btn btn-primary">
            <i class="bi bi-printer"></i> Print
        </button>
    </div>

    <div class="container-fluid print-wrap mt-2 mb-5">
        <iframe src="assets/download/tax_exempt.pdf#view=FitH&toolbar=0&navpanes=0&scrollbar=0" class="pdf-container" id="pdfViewer"></iframe>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function printPDF() {
            const pdfFrame = document.getElementById('pdfViewer');
            if (pdfFrame && pdfFrame.contentWindow) {
                try {
                    pdfFrame.contentWindow.focus();
                    pdfFrame.contentWindow.print();
                } catch (e) {
                    window.print();
                }
            } else {
                window.print();
            }
        }
    </script>
</body>
</html> 