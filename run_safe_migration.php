<?php
// Safe Database Migration Runner
include 'config.php';

echo "<h2>Safe Database Migration</h2>";
echo "<p>This will safely upgrade your database, handling existing columns and constraints.</p>";

try {
    // Read the safe migration SQL file
    $migration_sql = file_get_contents('database_migration_safe.sql');
    
    if (!$migration_sql) {
        throw new Exception("Could not read database_migration_safe.sql file");
    }
    
    // Split the SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $migration_sql)));
    
    echo "<h3>Running Safe Migration...</h3>";
    echo "<ul>";
    
    foreach ($statements as $statement) {
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue; // Skip empty statements and comments
        }
        
        try {
            $pdo->exec($statement);
            
            // Extract info for display
            if (strpos($statement, 'SET @sql') !== false) {
                echo "<li>✅ Checking/adding column or constraint</li>";
            } elseif (strpos($statement, 'UPDATE') !== false) {
                echo "<li>✅ Updated existing data</li>";
            } elseif (strpos($statement, 'CREATE TABLE') !== false) {
                echo "<li>✅ Created/verified table</li>";
            } elseif (strpos($statement, 'INSERT') !== false) {
                echo "<li>✅ Added default settings</li>";
            } else {
                echo "<li>✅ Executed statement</li>";
            }
            
        } catch (PDOException $e) {
            // Log errors but continue
            echo "<li>⚠️ Note: " . htmlspecialchars($e->getMessage()) . "</li>";
        }
    }
    
    echo "</ul>";
    echo "<h3>✅ Safe Migration Completed!</h3>";
    
    // Test the migration by checking if key columns exist
    echo "<h3>Verification:</h3>";
    echo "<ul>";
    
    try {
        $stmt = $pdo->query("SHOW COLUMNS FROM users LIKE 'email'");
        if ($stmt->fetch()) {
            echo "<li>✅ Email column exists in users table</li>";
        } else {
            echo "<li>❌ Email column missing from users table</li>";
        }
        
        $stmt = $pdo->query("SHOW COLUMNS FROM clients LIKE 'user_id'");
        if ($stmt->fetch()) {
            echo "<li>✅ User_id column exists in clients table</li>";
        } else {
            echo "<li>❌ User_id column missing from clients table</li>";
        }
        
        $stmt = $pdo->query("SHOW TABLES LIKE 'user_settings'");
        if ($stmt->fetch()) {
            echo "<li>✅ User_settings table exists</li>";
        } else {
            echo "<li>❌ User_settings table missing</li>";
        }
        
    } catch (PDOException $e) {
        echo "<li>❌ Verification error: " . htmlspecialchars($e->getMessage()) . "</li>";
    }
    
    echo "</ul>";
    echo "<p><strong>Next:</strong> <a href='register.php'>Test Registration</a> | <a href='login.php'>Login</a></p>";
    
} catch (Exception $e) {
    echo "<h3>❌ Migration Failed</h3>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Safe Database Migration - Klay Invoice</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
        .btn:hover { background: #0056b3; color: white; }
        ul, ol { margin: 10px 0; }
        li { margin: 5px 0; }
    </style>
</head>
<body>
</body>
</html>
